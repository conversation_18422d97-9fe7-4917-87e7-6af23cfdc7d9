// https://umijs.org/config/

import { join } from 'node:path';
import { defineConfig } from '@umijs/max';
import defaultSettings from './defaultSettings';
import proxy from './proxy';

import routes from './routes';

const { REACT_APP_ENV = 'dev' } = process.env;

/**
 * @name 使用公共路径
 * @description 部署时的路径，如果部署在非根目录下，需要配置这个变量
 * @doc https://umijs.org/docs/api/config#publicpath
 */
const PUBLIC_PATH: string = '/';

export default defineConfig({
  /**
   * @name 开启 hash 模式
   * @description 让 build 之后的产物包含 hash 后缀。通常用于增量发布和避免浏览器加载缓存。
   * @doc https://umijs.org/docs/api/config#hash
   */
  hash: true,

  publicPath: PUBLIC_PATH,


  /**
   * @name 路由的配置，不在路由中引入的文件不会编译
   * @description 只支持 path，component，routes，redirect，wrappers，title 的配置
   * @doc https://umijs.org/docs/guides/routes
   */
  // umi routes: https://umijs.org/docs/routing
  routes,

  /**
   * @name moment 的国际化配置
   * @description 如果对国际化没有要求，打开之后能减少js的包大小
   * @doc https://umijs.org/docs/api/config#ignoremomentlocale
   */
  ignoreMomentLocale: true,
  /**
   * @name 代理配置
   * @description 可以让你的本地服务器代理到你的服务器上，这样你就可以访问服务器的数据了
   * @see 要注意以下 代理只能在本地开发时使用，build 之后就无法使用了。
   * @doc 代理介绍 https://umijs.org/docs/guides/proxy
   * @doc 代理配置 https://umijs.org/docs/api/config#proxy
   */
  proxy: proxy[REACT_APP_ENV as keyof typeof proxy],
  /**
   * @name 快速热更新配置
   * @description 一个不错的热更新组件，更新时可以保留 state
   */
  fastRefresh: true,
  //============== 以下都是max的插件配置 ===============
  /**
   * @name 数据流插件
   * @@doc https://umijs.org/docs/max/data-flow
   */
  model: {},
  /**
   * 一个全局的初始数据流，可以用它在插件之间共享数据
   * @description 可以用来存放一些全局的数据，比如用户信息，或者一些全局的状态，全局初始状态在整个 Umi 项目的最开始创建。
   * @doc https://umijs.org/docs/max/data-flow#%E5%85%A8%E5%B1%80%E5%88%9D%E5%A7%8B%E7%8A%B6%E6%80%81
   */
  initialState: {},
  /**
   * @name layout 插件
   * @doc https://umijs.org/docs/max/layout-menu
   */
  title: '团队协作管理系统',
  layout: {
    locale: false,
    ...defaultSettings,
  },
  /**
   * @name moment2dayjs 插件
   * @description 将项目中的 moment 替换为 dayjs
   * @doc https://umijs.org/docs/max/moment2dayjs
   */
  moment2dayjs: {
    preset: 'antd',
    plugins: ['duration'],
  },

  /**
   * @name antd 插件
   * @description 内置了 babel import 插件
   * @doc https://umijs.org/docs/max/antd#antd
   */
  antd: {
    appConfig: {},
    configProvider: {
      theme: {
        cssVar: true,
        token: {
          fontFamily: 'AlibabaSans, sans-serif',
          // 专业商务配色方案
          colorPrimary: '#2563eb', // 专业蓝色 - 主色调
          colorSuccess: '#059669', // 专业绿色 - 成功状态
          colorWarning: '#d97706', // 专业橙色 - 警告状态
          colorError: '#dc2626', // 专业红色 - 错误状态
          colorInfo: '#0891b2', // 专业青色 - 信息状态

          // 中性色调 - 专业灰色系
          colorText: '#1f2937', // 主文本色
          colorTextSecondary: '#6b7280', // 次要文本色
          colorTextTertiary: '#9ca3af', // 三级文本色
          colorTextQuaternary: '#d1d5db', // 四级文本色

          // 背景色调
          colorBgContainer: '#ffffff', // 容器背景
          colorBgElevated: '#ffffff', // 浮层背景
          colorBgLayout: '#f8fafc', // 布局背景
          colorBgSpotlight: '#f1f5f9', // 聚焦背景
          colorBgMask: 'rgba(0, 0, 0, 0.45)', // 遮罩背景

          // 边框色调
          colorBorder: '#e5e7eb', // 默认边框
          colorBorderSecondary: '#f3f4f6', // 次要边框

          // 阴影和圆角
          borderRadius: 8, // 基础圆角
          borderRadiusLG: 12, // 大圆角
          borderRadiusSM: 6, // 小圆角
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
          boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',

          // 字体大小
          fontSize: 14,
          fontSizeLG: 16,
          fontSizeXL: 20,
          fontSizeSM: 12,

          // 行高
          lineHeight: 1.5714285714285714,
          lineHeightLG: 1.5,
          lineHeightSM: 1.66,

          // 间距
          padding: 16,
          paddingLG: 24,
          paddingXL: 32,
          paddingSM: 12,
          paddingXS: 8,
          paddingXXS: 4,

          margin: 16,
          marginLG: 24,
          marginXL: 32,
          marginSM: 12,
          marginXS: 8,
          marginXXS: 4,
        },
        components: {
          // 按钮组件专业化配置
          Button: {
            borderRadius: 8,
            controlHeight: 40,
            controlHeightSM: 32,
            controlHeightLG: 48,
            fontWeight: 500,
            primaryShadow: '0 2px 4px rgba(37, 99, 235, 0.2)',
          },
          // 卡片组件专业化配置
          Card: {
            borderRadius: 12,
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            headerBg: '#ffffff',
            headerHeight: 56,
            paddingLG: 24,
          },
          // 表格组件专业化配置
          Table: {
            borderRadius: 8,
            headerBg: '#f8fafc',
            headerColor: '#374151',
            headerSortActiveBg: '#f1f5f9',
            headerSortHoverBg: '#f3f4f6',
            rowHoverBg: '#f8fafc',
          },
          // 输入框组件专业化配置
          Input: {
            borderRadius: 8,
            controlHeight: 40,
            controlHeightSM: 32,
            controlHeightLG: 48,
            paddingInline: 12,
          },
          // 选择器组件专业化配置
          Select: {
            borderRadius: 8,
            controlHeight: 40,
            controlHeightSM: 32,
            controlHeightLG: 48,
          },
          // 模态框组件专业化配置
          Modal: {
            borderRadius: 12,
            headerBg: '#ffffff',
            contentBg: '#ffffff',
            footerBg: '#ffffff',
          },
          // 抽屉组件专业化配置
          Drawer: {
            borderRadius: 0,
            headerHeight: 56,
            bodyPadding: 24,
            footerPaddingBlock: 16,
            footerPaddingInline: 24,
          },
          // 消息组件专业化配置
          Message: {
            borderRadius: 8,
            contentPadding: '12px 16px',
          },
          // 通知组件专业化配置
          Notification: {
            borderRadius: 12,
            paddingMD: 20,
            paddingContentHorizontal: 20,
          },
          // 标签页组件专业化配置
          Tabs: {
            borderRadius: 8,
            cardBg: '#ffffff',
            cardHeight: 40,
            cardPadding: '8px 16px',
            cardPaddingSM: '6px 12px',
            cardPaddingLG: '10px 20px',
          },
          // 菜单组件专业化配置
          Menu: {
            borderRadius: 8,
            itemBorderRadius: 6,
            itemHeight: 40,
            itemPaddingInline: 16,
            subMenuItemBorderRadius: 6,
          },
        },
      },
    },
  },
  /**
   * @name 网络请求配置
   * @description 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
   * @doc https://umijs.org/docs/max/request
   */
  request: {},
  /**
   * @name 权限插件
   * @description 基于 initialState 的权限插件，必须先打开 initialState
   * @doc https://umijs.org/docs/max/access
   */
  access: {},
  /**
   * @name <head> 中额外的 script
   * @description 配置 <head> 中额外的 script
   */
  headScripts: [
    // 解决首次加载时白屏的问题
    { src: join(PUBLIC_PATH, 'scripts/loading.js'), async: true },
  ],
  //================ pro 插件配置 =================
  presets: ['umi-presets-pro'],
  /**
   * @name 是否开启 mako
   * @description 使用 mako 极速研发
   * @doc https://umijs.org/docs/api/config#mako
   */
  mako: {},
  esbuildMinifyIIFE: true,
  requestRecord: {},
  exportStatic: {},
});
