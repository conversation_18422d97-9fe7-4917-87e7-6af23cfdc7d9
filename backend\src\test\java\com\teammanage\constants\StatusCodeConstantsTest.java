package com.teammanage.constants;

import com.teammanage.entity.AccountSubscription.SubscriptionStatus;
import com.teammanage.entity.TeamInvitation.InvitationStatus;
import com.teammanage.enums.TeamRole;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 状态码常量类测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class StatusCodeConstantsTest {

    @Test
    public void testTeamRoleStatusCodes() {
        // 测试枚举到状态码的转换
        assertEquals(Integer.valueOf(100), TeamRole.StatusCodes.toCode(TeamRole.TEAM_CREATOR));
        assertEquals(Integer.valueOf(10), TeamRole.StatusCodes.toCode(TeamRole.TEAM_MEMBER));
        assertNull(TeamRole.StatusCodes.toCode(null));

        // 测试状态码到枚举的转换
        assertEquals(TeamRole.TEAM_CREATOR, TeamRole.StatusCodes.fromCode(100));
        assertEquals(TeamRole.TEAM_MEMBER, TeamRole.StatusCodes.fromCode(10));
        assertNull(TeamRole.StatusCodes.fromCode(999));
        assertNull(TeamRole.StatusCodes.fromCode(null));

        // 测试状态码有效性检查
        assertTrue(TeamRole.StatusCodes.isValidCode(100));
        assertTrue(TeamRole.StatusCodes.isValidCode(10));
        assertFalse(TeamRole.StatusCodes.isValidCode(999));
        assertFalse(TeamRole.StatusCodes.isValidCode(null));
    }

    @Test
    public void testInvitationStatusConstants() {
        // 测试枚举到状态码的转换
        assertEquals(Integer.valueOf(1), InvitationStatusConstants.toCode(InvitationStatus.PENDING));
        assertEquals(Integer.valueOf(2), InvitationStatusConstants.toCode(InvitationStatus.ACCEPTED));
        assertEquals(Integer.valueOf(3), InvitationStatusConstants.toCode(InvitationStatus.REJECTED));
        assertEquals(Integer.valueOf(4), InvitationStatusConstants.toCode(InvitationStatus.EXPIRED));
        assertEquals(Integer.valueOf(5), InvitationStatusConstants.toCode(InvitationStatus.CANCELLED));
        assertNull(InvitationStatusConstants.toCode(null));

        // 测试状态码到枚举的转换
        assertEquals(InvitationStatus.PENDING, InvitationStatusConstants.fromCode(1));
        assertEquals(InvitationStatus.ACCEPTED, InvitationStatusConstants.fromCode(2));
        assertEquals(InvitationStatus.REJECTED, InvitationStatusConstants.fromCode(3));
        assertEquals(InvitationStatus.EXPIRED, InvitationStatusConstants.fromCode(4));
        assertEquals(InvitationStatus.CANCELLED, InvitationStatusConstants.fromCode(5));
        assertNull(InvitationStatusConstants.fromCode(999));
        assertNull(InvitationStatusConstants.fromCode(null));

        // 测试显示名称
        assertEquals("待确认", InvitationStatusConstants.getDisplayName(1));
        assertEquals("已确认", InvitationStatusConstants.getDisplayName(2));
        assertEquals("已拒绝", InvitationStatusConstants.getDisplayName(3));
        assertEquals("已过期", InvitationStatusConstants.getDisplayName(4));
        assertEquals("已取消", InvitationStatusConstants.getDisplayName(5));
        assertNull(InvitationStatusConstants.getDisplayName(999));

        // 测试业务逻辑方法
        assertTrue(InvitationStatusConstants.canBeResponded(1)); // PENDING
        assertFalse(InvitationStatusConstants.canBeResponded(2)); // ACCEPTED
        assertFalse(InvitationStatusConstants.canBeResponded(3)); // REJECTED

        assertTrue(InvitationStatusConstants.canBeCancelled(1)); // PENDING
        assertFalse(InvitationStatusConstants.canBeCancelled(2)); // ACCEPTED

        assertTrue(InvitationStatusConstants.isExpired(4)); // EXPIRED
        assertFalse(InvitationStatusConstants.isExpired(1)); // PENDING
    }

    @Test
    public void testSubscriptionStatusConstants() {
        // 测试枚举到状态码的转换
        assertEquals(Integer.valueOf(1), SubscriptionStatusConstants.toCode(SubscriptionStatus.ACTIVE));
        assertEquals(Integer.valueOf(2), SubscriptionStatusConstants.toCode(SubscriptionStatus.EXPIRED));
        assertEquals(Integer.valueOf(3), SubscriptionStatusConstants.toCode(SubscriptionStatus.CANCELED));
        assertNull(SubscriptionStatusConstants.toCode(null));

        // 测试状态码到枚举的转换
        assertEquals(SubscriptionStatus.ACTIVE, SubscriptionStatusConstants.fromCode(1));
        assertEquals(SubscriptionStatus.EXPIRED, SubscriptionStatusConstants.fromCode(2));
        assertEquals(SubscriptionStatus.CANCELED, SubscriptionStatusConstants.fromCode(3));
        assertNull(SubscriptionStatusConstants.fromCode(999));
        assertNull(SubscriptionStatusConstants.fromCode(null));

        // 测试显示名称
        assertEquals("激活", SubscriptionStatusConstants.getDisplayName(1));
        assertEquals("过期", SubscriptionStatusConstants.getDisplayName(2));
        assertEquals("取消", SubscriptionStatusConstants.getDisplayName(3));
        assertNull(SubscriptionStatusConstants.getDisplayName(999));

        // 测试业务逻辑方法
        assertTrue(SubscriptionStatusConstants.isActive(1)); // ACTIVE
        assertFalse(SubscriptionStatusConstants.isActive(2)); // EXPIRED
        assertFalse(SubscriptionStatusConstants.isActive(3)); // CANCELED

        assertTrue(SubscriptionStatusConstants.isExpired(2)); // EXPIRED
        assertFalse(SubscriptionStatusConstants.isExpired(1)); // ACTIVE

        assertTrue(SubscriptionStatusConstants.isCanceled(3)); // CANCELED
        assertFalse(SubscriptionStatusConstants.isCanceled(1)); // ACTIVE
    }

    @Test
    public void testTodoConstants() {
        // 测试状态常量
        assertEquals("未完成", TodoConstants.Status.getDisplayName(0));
        assertEquals("已完成", TodoConstants.Status.getDisplayName(1));
        assertNull(TodoConstants.Status.getDisplayName(999));

        assertTrue(TodoConstants.Status.isValidCode(0));
        assertTrue(TodoConstants.Status.isValidCode(1));
        assertFalse(TodoConstants.Status.isValidCode(999));

        assertTrue(TodoConstants.Status.isPending(0));
        assertFalse(TodoConstants.Status.isPending(1));

        assertTrue(TodoConstants.Status.isCompleted(1));
        assertFalse(TodoConstants.Status.isCompleted(0));

        // 测试优先级常量
        assertEquals("低", TodoConstants.Priority.getDisplayName(1));
        assertEquals("中", TodoConstants.Priority.getDisplayName(2));
        assertEquals("高", TodoConstants.Priority.getDisplayName(3));
        assertNull(TodoConstants.Priority.getDisplayName(999));

        assertTrue(TodoConstants.Priority.isValidCode(1));
        assertTrue(TodoConstants.Priority.isValidCode(2));
        assertTrue(TodoConstants.Priority.isValidCode(3));
        assertFalse(TodoConstants.Priority.isValidCode(999));

        assertTrue(TodoConstants.Priority.isLow(1));
        assertTrue(TodoConstants.Priority.isMedium(2));
        assertTrue(TodoConstants.Priority.isHigh(3));
        assertFalse(TodoConstants.Priority.isHigh(1));
    }
}
