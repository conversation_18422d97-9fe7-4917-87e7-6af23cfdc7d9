import type { ProLayoutProps } from '@ant-design/pro-components';

const Settings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'light',
  colorPrimary: '#2563eb', // 更新为专业蓝色
  layout: 'side',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: '团队协作管理系统',
  pwa: false,
  logo: '/logo.svg',
  iconfontUrl: '',
  token: {
    // 专业商务主题令牌
    colorPrimary: '#2563eb',
    colorSuccess: '#059669',
    colorWarning: '#d97706',
    colorError: '#dc2626',
    colorInfo: '#0891b2',
    colorText: '#1f2937',
    colorTextSecondary: '#6b7280',
    colorBgContainer: '#ffffff',
    colorBgLayout: '#f8fafc',
    borderRadius: 8,
    borderRadiusLG: 12,
  },
};

export default Settings;
