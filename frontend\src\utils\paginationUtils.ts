/**
 * 分页工具函数
 * 
 * 提供分页相关的URL参数处理、状态管理等功能
 */

import { useSearchParams, useNavigate } from '@umijs/max';
import { useCallback, useMemo, useState } from 'react';

/**
 * 分页参数接口
 */
export interface PaginationParams {
  current: number;
  pageSize: number;
}

/**
 * 分页配置接口
 */
export interface PaginationConfig {
  defaultCurrent?: number;
  defaultPageSize?: number;
  pageSizeOptions?: string[];
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: (total: number, range: [number, number]) => string;
}

/**
 * 分页Hook返回值接口
 */
export interface UsePaginationResult {
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, size?: number) => void;
    onShowSizeChange: (current: number, size: number) => void;
    showSizeChanger: boolean;
    showQuickJumper: boolean;
    showTotal: (total: number, range: [number, number]) => string;
    pageSizeOptions: string[];
  };
  updateTotal: (total: number) => void;
}

/**
 * 默认分页配置
 */
const DEFAULT_CONFIG: Required<PaginationConfig> = {
  defaultCurrent: 1,
  defaultPageSize: 10,
  pageSizeOptions: ['10', '20', '50', '100'],
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`,
};

/**
 * URL参数键名
 */
const URL_KEYS = {
  PAGE: 'page',
  PAGE_SIZE: 'pageSize',
} as const;

/**
 * 从URL参数中解析分页参数
 */
export const parsePaginationFromURL = (
  searchParams: URLSearchParams,
  config: PaginationConfig = {}
): PaginationParams => {
  const mergedConfig = { ...DEFAULT_CONFIG, ...config };
  
  const current = parseInt(searchParams.get(URL_KEYS.PAGE) || '1', 10);
  const pageSize = parseInt(searchParams.get(URL_KEYS.PAGE_SIZE) || String(mergedConfig.defaultPageSize), 10);
  
  return {
    current: current > 0 ? current : mergedConfig.defaultCurrent,
    pageSize: pageSize > 0 ? pageSize : mergedConfig.defaultPageSize,
  };
};

/**
 * 将分页参数更新到URL
 */
export const updatePaginationInURL = (
  searchParams: URLSearchParams,
  params: Partial<PaginationParams>
): URLSearchParams => {
  const newSearchParams = new URLSearchParams(searchParams);
  
  if (params.current !== undefined) {
    if (params.current === 1) {
      // 第一页时移除page参数，保持URL简洁
      newSearchParams.delete(URL_KEYS.PAGE);
    } else {
      newSearchParams.set(URL_KEYS.PAGE, String(params.current));
    }
  }
  
  if (params.pageSize !== undefined) {
    if (params.pageSize === DEFAULT_CONFIG.defaultPageSize) {
      // 默认页大小时移除pageSize参数，保持URL简洁
      newSearchParams.delete(URL_KEYS.PAGE_SIZE);
    } else {
      newSearchParams.set(URL_KEYS.PAGE_SIZE, String(params.pageSize));
    }
  }
  
  return newSearchParams;
};

/**
 * 分页Hook
 * 
 * 提供与URL同步的分页功能
 * 
 * @param config 分页配置
 * @returns 分页相关的状态和方法
 */
export const usePagination = (config: PaginationConfig = {}): UsePaginationResult => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const mergedConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);
  
  // 从URL解析当前分页参数
  const paginationParams = useMemo(() => 
    parsePaginationFromURL(searchParams, mergedConfig), 
    [searchParams, mergedConfig]
  );
  
  // 总数状态（需要外部传入）
  const [total, setTotal] = useState(0);
  
  // 更新URL参数
  const updateURL = useCallback((params: Partial<PaginationParams>) => {
    const newSearchParams = updatePaginationInURL(searchParams, params);
    const newSearch = newSearchParams.toString();
    
    // 只有参数真正改变时才更新URL
    if (newSearch !== searchParams.toString()) {
      navigate({
        search: newSearch ? `?${newSearch}` : '',
      }, { replace: true });
    }
  }, [searchParams, navigate]);
  
  // 页码改变处理
  const onChange = useCallback((page: number, size?: number) => {
    const newParams: Partial<PaginationParams> = { current: page };
    if (size !== undefined && size !== paginationParams.pageSize) {
      newParams.pageSize = size;
      // 改变页大小时重置到第一页
      newParams.current = 1;
    }
    updateURL(newParams);
  }, [paginationParams.pageSize, updateURL]);
  
  // 页大小改变处理
  const onShowSizeChange = useCallback((current: number, size: number) => {
    updateURL({ current: 1, pageSize: size });
  }, [updateURL]);
  
  // 更新总数
  const updateTotal = useCallback((newTotal: number) => {
    setTotal(newTotal);
  }, []);
  
  return {
    pagination: {
      current: paginationParams.current,
      pageSize: paginationParams.pageSize,
      total,
      onChange,
      onShowSizeChange,
      showSizeChanger: mergedConfig.showSizeChanger,
      showQuickJumper: mergedConfig.showQuickJumper,
      showTotal: mergedConfig.showTotal,
      pageSizeOptions: mergedConfig.pageSizeOptions,
    },
    updateTotal,
  };
};

/**
 * 计算分页数据的起始和结束索引
 */
export const calculatePaginationRange = (current: number, pageSize: number): [number, number] => {
  const start = (current - 1) * pageSize;
  const end = start + pageSize;
  return [start, end];
};

/**
 * 对数组数据进行分页处理
 */
export const paginateArray = <T>(
  data: T[],
  current: number,
  pageSize: number
): { list: T[]; total: number } => {
  const [start, end] = calculatePaginationRange(current, pageSize);
  return {
    list: data.slice(start, end),
    total: data.length,
  };
};
