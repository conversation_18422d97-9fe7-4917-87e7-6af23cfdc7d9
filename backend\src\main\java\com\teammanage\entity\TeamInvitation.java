package com.teammanage.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 团队邀请实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("team_invitation")
public class TeamInvitation extends BaseEntity {

    /**
     * 团队ID
     */
    @NotNull(message = "团队ID不能为空")
    private Long teamId;

    /**
     * 邀请人ID
     */
    @NotNull(message = "邀请人ID不能为空")
    private Long inviterId;

    /**
     * 被邀请人邮箱
     */
    @NotBlank(message = "被邀请人邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 255, message = "邮箱长度不能超过255个字符")
    private String inviteeEmail;

    /**
     * 被邀请人ID（邀请时可能为空）
     */
    private Long inviteeId;

    /**
     * 邀请状态
     */
    private InvitationStatus status;

    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invitedAt;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime respondedAt;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    /**
     * 邀请消息
     */
    private String message;

    /**
     * 邀请状态枚举
     */
    public enum InvitationStatus {
        PENDING,    // 待确认
        ACCEPTED,   // 已确认
        REJECTED,   // 已拒绝
        EXPIRED,    // 已过期
        CANCELLED   // 已取消
    }

    /**
     * 检查邀请是否已过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查邀请是否可以被响应
     */
    public boolean canBeResponded() {
        return status == InvitationStatus.PENDING && !isExpired();
    }

    /**
     * 检查邀请是否可以被取消
     */
    public boolean canBeCancelled() {
        return status == InvitationStatus.PENDING;
    }

}
