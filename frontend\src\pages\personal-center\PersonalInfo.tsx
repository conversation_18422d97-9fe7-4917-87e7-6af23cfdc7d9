import {
  QuestionCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Grid,
  Space,
  Spin,
  Typography,
} from 'antd';
import { ProCard } from '@ant-design/pro-components';

import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserProfileDetailResponse } from '@/types/api';
import UnifiedSettingsModal from './UnifiedSettingsModal';
import UserInfoPopover from './UserInfoPopover';
import './TooltipFix.module.css'; // 导入 Tooltip 修复样式

const { Title, Text } = Typography;

/**
 * 个人信息组件
 *
 * 显示用户的基本个人信息，采用简洁的卡片设计。
 * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名（支持气泡卡片显示详细信息）
 * 3. 显示最后登录时间和登录团队
 * 4. 提供设置入口
 *
 * 响应式设计：
 * - xs/sm: 移动端优化，紧凑布局
 * - md+: 桌面端，标准布局
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */
const PersonalInfo: React.FC = () => {
  /**
   * 响应式检测
   */
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  /**
   * 用户详细信息状态管理
   */
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: '',
    position: '',
    email: '',
    telephone: '',
    registerDate: '',
    lastLoginTime: '',
    lastLoginTeam: '',
    teamCount: 0,
    avatar: '',
  });

  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // Modal状态管理
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  // 获取用户数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetail = await UserService.getUserProfileDetail();
        setUserInfo(userDetail);
        setUserInfoError(null);
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        setUserInfoError('获取用户详细信息失败，请稍后重试');
      } finally {
        setUserInfoLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return (
    <>
      <ProCard
        style={{
          marginBottom: 16,
          borderRadius: 16,
          border: '1px solid rgba(37, 99, 235, 0.08)',
          position: 'relative',
          background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',
          boxShadow: '0 4px 20px rgba(37, 99, 235, 0.06)',
        }}
        bodyStyle={{
          padding: screens.md ? '20px 24px' : '16px',
        }}
      >
        {/* 右上角图标区域 */}
        <div style={{
          position: 'absolute',
          top: screens.md ? '20px' : '16px',
          right: screens.md ? '20px' : '16px',
          zIndex: 10,
        }}>
          <Space size={screens.md ? 16 : 12}>
            <UserInfoPopover userInfo={userInfo}>
              <QuestionCircleOutlined
                style={{
                  fontSize: screens.md ? 20 : 18,
                  color: '#6b7280',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  padding: '8px',
                  borderRadius: '8px',
                  background: 'rgba(37, 99, 235, 0.04)',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#2563eb';
                  e.currentTarget.style.background = 'rgba(37, 99, 235, 0.1)';
                  e.currentTarget.style.transform = 'scale(1.05)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#6b7280';
                  e.currentTarget.style.background = 'rgba(37, 99, 235, 0.04)';
                  e.currentTarget.style.transform = 'scale(1)';
                }}
              />
            </UserInfoPopover>
            <SettingOutlined
              style={{
                fontSize: screens.md ? 20 : 18,
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                padding: '8px',
                borderRadius: '8px',
                background: 'rgba(37, 99, 235, 0.04)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#2563eb';
                e.currentTarget.style.background = 'rgba(37, 99, 235, 0.1)';
                e.currentTarget.style.transform = 'scale(1.05)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#6b7280';
                e.currentTarget.style.background = 'rgba(37, 99, 235, 0.04)';
                e.currentTarget.style.transform = 'scale(1)';
              }}
              onClick={() => setSettingsModalVisible(true)}
            />
          </Space>
        </div>

        {userInfoError ? (
          <Alert
            message="个人信息加载失败"
            description={userInfoError}
            type="error"
            showIcon
            style={{
              borderRadius: 12,
              border: 'none',
            }}
          />
        ) : (
          <Spin spinning={userInfoLoading}>
            {/* 问候区域 */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: screens.md ? '16px 0' : '12px 0',
              marginTop: screens.md ? '20px' : '16px', // 为右上角图标留出空间
            }}>
              {/* 问候语 */}
              <Typography.Title
                level={screens.md ? 3 : 4}
                style={{
                  margin: 0,
                  fontSize: screens.md ? 24 : 20,
                  color: '#1f2937',
                  textAlign: 'center',
                  fontWeight: 600,
                  background: 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                }}
              >
                您好，{userInfo.name || '加载中...'}
              </Typography.Title>
            </div>
          </Spin>
        )}
      </ProCard>

      {/* 统一设置Modal */}
      <UnifiedSettingsModal
        visible={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        userInfo={userInfo}
        onSuccess={() => {
          // 可以在这里刷新用户信息
          console.log('设置操作成功');
        }}
      />
    </>
  );
};

export default PersonalInfo;
