import { EditOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText, ProFormSelect } from '@ant-design/pro-components';
import { message } from 'antd';
import React from 'react';
import { TodoService } from '@/services/todo';
import type { UpdateTodoRequest, TodoResponse } from '@/types/api';

interface EditTodoModalFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  todo: TodoResponse | null;
  onSuccess: (todo: TodoResponse) => void;
}

/**
 * 编辑待办事项模态框组件
 */
const EditTodoModalForm: React.FC<EditTodoModalFormProps> = ({
  open,
  onOpenChange,
  todo,
  onSuccess,
}) => {
  const handleSubmit = async (values: UpdateTodoRequest) => {
    if (!todo) {
      console.error('没有正在编辑的待办事项');
      return false;
    }

    try {
      // 等待一小段时间以显示提交状态
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedTodo = await TodoService.updateTodo(todo.id, {
        title: values.title,
        priority: values.priority,
      });

      message.success('任务更新成功！');
      onSuccess(updatedTodo);
      return true; // 返回true表示操作成功，ModalForm会自动关闭
    } catch (error) {
      console.error('更新待办事项失败:', error);
      return false; // 返回false表示操作失败，ModalForm不会关闭
    }
  };

  return (
    <ModalForm<UpdateTodoRequest>
      title="编辑待办事项"
      open={open}
      onOpenChange={onOpenChange}
      modalProps={{
        destroyOnClose: true,
        maskClosable: true,
      }}
      submitTimeout={2000}
      onFinish={handleSubmit}
      submitter={{
        searchConfig: {
          submitText: '更新任务',
          resetText: '取消',
        },
        submitButtonProps: {
          icon: <EditOutlined />,
        },
      }}
      initialValues={todo ? {
        title: todo.title,
        priority: todo.priority,
      } : undefined}
    >
      <ProFormText
        name="title"
        label="任务名称"
        placeholder="请输入任务名称"
        rules={[
          { required: true, message: '请输入任务名称' },
          { max: 100, message: '任务名称不能超过100个字符' },
          { whitespace: true, message: '任务名称不能为空白字符' }
        ]}
      />

      <ProFormSelect
        name="priority"
        label="优先级"
        placeholder="请选择优先级"
        rules={[{ required: true, message: '请选择优先级' }]}
        options={[
          { value: 3, label: '高优先级' },
          { value: 2, label: '中优先级' },
          { value: 1, label: '低优先级' },
        ]}
      />
    </ModalForm>
  );
};

export default EditTodoModalForm;
