// @ts-nocheck
// This file is generated by Um<PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';
import {
  ConfigProvider,
  App,
} from 'antd';
import { ApplyPluginsType } from 'umi';
import { getPluginManager } from '../core/plugin';
import { AntdConfigContext, AntdConfigContextSetter } from './context';
import merge from 'F:/Project/teamAuth/frontend/node_modules/lodash/merge'

let cacheAntdConfig = null;

const getAntdConfig = () => {
  if(!cacheAntdConfig){
    cacheAntdConfig = getPluginManager().applyPlugins({
      key: 'antd',
      type: ApplyPluginsType.modify,
      initialValue: {
        ...{"theme":{"cssVar":true,"token":{"fontFamily":"AlibabaSans, sans-serif","colorPrimary":"#2563eb","colorSuccess":"#059669","colorWarning":"#d97706","colorError":"#dc2626","colorInfo":"#0891b2","colorText":"#1f2937","colorTextSecondary":"#6b7280","colorTextTertiary":"#9ca3af","colorTextQuaternary":"#d1d5db","colorBgContainer":"#ffffff","colorBgElevated":"#ffffff","colorBgLayout":"#f8fafc","colorBgSpotlight":"#f1f5f9","colorBgMask":"rgba(0, 0, 0, 0.45)","colorBorder":"#e5e7eb","colorBorderSecondary":"#f3f4f6","borderRadius":8,"borderRadiusLG":12,"borderRadiusSM":6,"boxShadow":"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)","boxShadowSecondary":"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)","fontSize":14,"fontSizeLG":16,"fontSizeXL":20,"fontSizeSM":12,"lineHeight":1.5714285714285714,"lineHeightLG":1.5,"lineHeightSM":1.66,"padding":16,"paddingLG":24,"paddingXL":32,"paddingSM":12,"paddingXS":8,"paddingXXS":4,"margin":16,"marginLG":24,"marginXL":32,"marginSM":12,"marginXS":8,"marginXXS":4},"components":{"Button":{"borderRadius":8,"controlHeight":40,"controlHeightSM":32,"controlHeightLG":48,"fontWeight":500,"primaryShadow":"0 2px 4px rgba(37, 99, 235, 0.2)"},"Card":{"borderRadius":12,"boxShadow":"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)","headerBg":"#ffffff","headerHeight":56,"paddingLG":24},"Table":{"borderRadius":8,"headerBg":"#f8fafc","headerColor":"#374151","headerSortActiveBg":"#f1f5f9","headerSortHoverBg":"#f3f4f6","rowHoverBg":"#f8fafc"},"Input":{"borderRadius":8,"controlHeight":40,"controlHeightSM":32,"controlHeightLG":48,"paddingInline":12},"Select":{"borderRadius":8,"controlHeight":40,"controlHeightSM":32,"controlHeightLG":48},"Modal":{"borderRadius":12,"headerBg":"#ffffff","contentBg":"#ffffff","footerBg":"#ffffff"},"Drawer":{"borderRadius":0,"headerHeight":56,"bodyPadding":24,"footerPaddingBlock":16,"footerPaddingInline":24},"Message":{"borderRadius":8,"contentPadding":"12px 16px"},"Notification":{"borderRadius":12,"paddingMD":20,"paddingContentHorizontal":20},"Tabs":{"borderRadius":8,"cardBg":"#ffffff","cardHeight":40,"cardPadding":"8px 16px","cardPaddingSM":"6px 12px","cardPaddingLG":"10px 20px"},"Menu":{"borderRadius":8,"itemBorderRadius":6,"itemHeight":40,"itemPaddingInline":16,"subMenuItemBorderRadius":6}}}},
        appConfig: {},
      },
    });
    if (!cacheAntdConfig.theme) {
      cacheAntdConfig.theme = {};
    }
  }
  return cacheAntdConfig;
}

function AntdProvider({ children }) {
  let container = children;

  const [antdConfig, _setAntdConfig] = React.useState(() => {
    const {
      appConfig: _,
      ...finalConfigProvider
    } = getAntdConfig();
    return finalConfigProvider
  });
  const setAntdConfig: typeof _setAntdConfig = (newConfig) => {
    _setAntdConfig(prev => {
      return merge({}, prev, typeof newConfig === 'function' ? newConfig(prev) : newConfig)
    })
  }


  if (antdConfig.prefixCls) {
    ConfigProvider.config({
      prefixCls: antdConfig.prefixCls,
    });
  };

  if (antdConfig.iconPrefixCls) {
    // Icons in message need to set iconPrefixCls via ConfigProvider.config()
    ConfigProvider.config({
      iconPrefixCls: antdConfig.iconPrefixCls,
    });
  };

  if (antdConfig.theme) {
    // Pass config theme to static method
    ConfigProvider.config({
      theme: antdConfig.theme,
    });
  }

  container = <ConfigProvider {...antdConfig}>{container}</ConfigProvider>;


  container = (
    <AntdConfigContextSetter.Provider value={setAntdConfig}>
      <AntdConfigContext.Provider value={antdConfig}>
        {container}
      </AntdConfigContext.Provider>
    </AntdConfigContextSetter.Provider>
  )

  return container;
}

export function rootContainer(children) {
  return (
    <AntdProvider>
      {children}
    </AntdProvider>
  );
}

// The App component should be under ConfigProvider
// plugin-locale has other ConfigProvider
export function innerProvider(container: any) {
  const {
    appConfig: finalAppConfig = {},
  } = getAntdConfig();
  return <App {...finalAppConfig}>{container}</App>;
}
