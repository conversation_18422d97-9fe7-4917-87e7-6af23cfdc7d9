@font-face {
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*1GSgSYDD_aIAAAAAQsAAAAgAegCCAQ/AlibabaSans-Light.woff2")
    format("woff2");
}
@font-face {
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*2zEUQqnPNesAAAAAQtAAAAgAegCCAQ/AlibabaSans-Regular.woff2")
    format("woff2");
}
@font-face {
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*E_cxRbMlZqUAAAAAQuAAAAgAegCCAQ/AlibabaSans-Medium.woff2")
    format("woff2");
}
@font-face {
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*E_cxRbMlZqUAAAAAQuAAAAgAegCCAQ/AlibabaSans-Bold.woff2")
    format("woff2");
}
@font-face {
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*E_cxRbMlZqUAAAAAQuAAAAgAegCCAQ/AlibabaSans-Heavy.woff2")
    format("woff2");
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family:
    AlibabaSans, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  /* 防止水平滚动条 */
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

/* 全局box-sizing设置，防止padding和border导致溢出 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
  /* 防止布局溢出 */
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

/* 防止页面转换期间的水平滚动条 */
.ant-pro-layout,
.ant-pro-layout-content,
.ant-layout-content {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

/* 确保所有主要容器都不会溢出 */
.ant-pro-page-container,
.ant-pro-page-container-children-content {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* 防止表格和其他组件导致的水平滚动 */
.ant-table-wrapper {
  width: 100%;
  overflow-x: auto;
}

/* 确保模态框和抽屉不会导致水平滚动 */
.ant-modal,
.ant-drawer {
  max-width: 100vw;
}

/* 专业商务主题样式增强 */
:root {
  /* 专业配色变量 */
  --primary-color: #2563eb;
  --primary-color-hover: #3b82f6;
  --primary-color-active: #1d4ed8;
  --success-color: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --info-color: #0891b2;

  /* 中性色调 */
  --text-color: #1f2937;
  --text-color-secondary: #6b7280;
  --text-color-tertiary: #9ca3af;
  --border-color: #e5e7eb;
  --border-color-secondary: #f3f4f6;

  /* 背景色调 */
  --bg-container: #ffffff;
  --bg-layout: #f8fafc;
  --bg-spotlight: #f1f5f9;

  /* 阴影 */
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 圆角 */
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
}

/* 专业卡片样式增强 */
.ant-card {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color-secondary);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--border-color);
  }

  .ant-card-head {
    border-bottom: 1px solid var(--border-color-secondary);
    background: var(--bg-container);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;

    .ant-card-head-title {
      color: var(--text-color);
      font-weight: 600;
    }
  }

  .ant-card-body {
    background: var(--bg-container);
  }
}

/* 专业按钮样式增强 */
.ant-btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.3s ease;

  &.ant-btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);

    &:hover {
      background: var(--primary-color-hover);
      border-color: var(--primary-color-hover);
      box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
    }

    &:active {
      background: var(--primary-color-active);
      border-color: var(--primary-color-active);
    }
  }
}

/* 专业表格样式增强 */
.ant-table {
  border-radius: var(--border-radius);
  overflow: hidden;

  .ant-table-thead > tr > th {
    background: var(--bg-spotlight);
    color: var(--text-color);
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
  }

  .ant-table-tbody > tr:hover > td {
    background: var(--bg-spotlight);
  }
}

/* 专业输入框样式增强 */
.ant-input,
.ant-select-selector {
  border-radius: var(--border-radius);
  border-color: var(--border-color);

  &:hover {
    border-color: var(--primary-color-hover);
  }

  &:focus,
  &.ant-select-focused .ant-select-selector {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
  }
}

/* 专业模态框样式增强 */
.ant-modal {
  .ant-modal-content {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
  }

  .ant-modal-header {
    background: var(--bg-container);
    border-bottom: 1px solid var(--border-color-secondary);

    .ant-modal-title {
      color: var(--text-color);
      font-weight: 600;
    }
  }

  .ant-modal-body {
    background: var(--bg-container);
  }

  .ant-modal-footer {
    background: var(--bg-container);
    border-top: 1px solid var(--border-color-secondary);
  }
}

/* 专业菜单样式增强 */
.ant-menu {
  border-radius: var(--border-radius);

  .ant-menu-item,
  .ant-menu-submenu-title {
    border-radius: 6px;
    margin: 2px 8px;
    width: calc(100% - 16px);

    &:hover {
      background: var(--bg-spotlight);
    }

    &.ant-menu-item-selected {
      background: rgba(37, 99, 235, 0.1);
      color: var(--primary-color);
    }
  }
}

/* 专业标签页样式增强 */
.ant-tabs {
  .ant-tabs-tab {
    border-radius: var(--border-radius) var(--border-radius) 0 0;

    &.ant-tabs-tab-active {
      background: var(--bg-container);

      .ant-tabs-tab-btn {
        color: var(--primary-color);
        font-weight: 600;
      }
    }
  }

  .ant-tabs-content-holder {
    background: var(--bg-container);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
  }
}

/* 响应式增强 */
@media (max-width: 576px) {
  .ant-card {
    margin: 8px;
    border-radius: var(--border-radius);
  }

  .ant-modal {
    margin: 16px;

    .ant-modal-content {
      border-radius: var(--border-radius);
    }
  }

  .ant-btn {
    height: 36px;
    padding: 0 12px;
    font-size: 14px;
  }
}

@media (min-width: 1200px) {
  .ant-card {
    border-radius: var(--border-radius-xl);
  }

  .ant-modal .ant-modal-content {
    border-radius: var(--border-radius-xl);
  }
}
