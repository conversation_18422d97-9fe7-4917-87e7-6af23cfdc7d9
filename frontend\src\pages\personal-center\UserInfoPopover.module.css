/* 用户信息气泡卡片样式 */

/* 气泡卡片内容区域 */
.popoverContent {
  padding: 16px;
  min-width: 320px;
  max-width: 380px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(37, 99, 235, 0.12);
}

/* 气泡卡片标题 */
.popoverTitle {
  padding: 0 0 12px 0;
  border-bottom: 1px solid rgba(37, 99, 235, 0.08);
  margin-bottom: 16px;
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
}

/* 信息项容器 */
.infoItem {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 0 0 8px 0;
  position: relative;
  background: rgba(248, 250, 252, 0.5);
  border: 1px solid rgba(37, 99, 235, 0.04);
}

/* 信息项悬停效果 */
.infoItem:hover {
  background: rgba(37, 99, 235, 0.06);
  border-color: rgba(37, 99, 235, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.08);
}

/* 邮箱项悬停效果 */
.infoItem.email:hover {
  background: rgba(24, 144, 255, 0.04);
}

/* 电话项悬停效果 */
.infoItem.phone:hover {
  background: rgba(82, 196, 26, 0.04);
}

/* 注册时间项悬停效果 */
.infoItem.register:hover {
  background: rgba(114, 46, 209, 0.04);
}

/* 最后登录项悬停效果 */
.infoItem.lastLogin:hover {
  background: rgba(250, 140, 22, 0.04);
}

/* 登录团队项悬停效果 */
.infoItem.team:hover {
  background: rgba(19, 194, 194, 0.04);
}

/* 图标包装器 */
.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border: 1px solid rgba(37, 99, 235, 0.12);
  flex-shrink: 0;
  transition: all 0.2s ease;
}

/* 图标样式 */
.icon {
  font-size: 14px;
  font-weight: 600;
}

/* 信息内容区域 */
.infoContent {
  flex: 1;
  min-width: 0;
}

/* 标签样式 */
.label {
  display: block;
  font-size: 12px;
  line-height: 1.3;
  margin-bottom: 4px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 值样式 */
.value {
  display: block;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  color: #1f2937;
  word-break: break-all;
}

/* 触发器样式 */
.trigger {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  padding: 4px 6px;
  margin: -4px -6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.trigger:hover {
  background: rgba(24, 144, 255, 0.06);
  transform: scale(1.05);
}

/* 问号图标样式 */
.questionIcon {
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 6px;
  border-radius: 8px;
  background: rgba(37, 99, 235, 0.04);
  border: 1px solid rgba(37, 99, 235, 0.08);
}

.questionIcon:hover {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.15);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}

/* 设置图标样式 */
.settingIcon {
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 6px;
  border-radius: 8px;
  background: rgba(37, 99, 235, 0.04);
  border: 1px solid rgba(37, 99, 235, 0.08);
}

.settingIcon:hover {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.15);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .popoverContent {
    min-width: 280px;
    max-width: 320px;
  }
  
  .infoItem {
    gap: 10px;
    padding: 8px 0;
  }
  
  .iconWrapper {
    width: 24px;
    height: 24px;
  }
  
  .icon {
    font-size: 12px;
  }
  
  .label {
    font-size: 11px;
  }
  
  .value {
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .popoverContent {
    min-width: 260px;
    max-width: 300px;
  }
  
  .popoverTitle {
    font-size: 13px;
    padding: 6px 0 10px 0;
    margin-bottom: 10px;
  }
  
  .infoItem {
    gap: 8px;
    padding: 6px 0;
  }
  
  .iconWrapper {
    width: 22px;
    height: 22px;
  }
  
  .icon {
    font-size: 11px;
  }
  
  .label {
    font-size: 10px;
    margin-bottom: 2px;
  }
  
  .value {
    font-size: 12px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.popoverContent {
  animation: fadeIn 0.2s ease-out;
}

/* 分割线样式 */
.divider {
  margin: 8px 0;
  border-color: #f0f0f0;
}

/* 复制按钮样式优化 */
.value :global(.ant-typography-copy) {
  color: #8c8c8c;
  margin-left: 4px;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.value:hover :global(.ant-typography-copy) {
  opacity: 1;
  color: #1890ff;
}
