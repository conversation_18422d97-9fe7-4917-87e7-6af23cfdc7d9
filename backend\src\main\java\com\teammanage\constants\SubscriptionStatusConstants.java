package com.teammanage.constants;

import com.teammanage.entity.AccountSubscription.SubscriptionStatus;
import java.util.HashMap;
import java.util.Map;

/**
 * 订阅状态常量类
 * 
 * 定义订阅状态的标识符和映射关系，支持状态标识符格式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SubscriptionStatusConstants {
    
    /** 激活状态码 */
    public static final int ACTIVE_CODE = 1;
    /** 过期状态码 */
    public static final int EXPIRED_CODE = 2;
    /** 取消状态码 */
    public static final int CANCELED_CODE = 3;
    
    /** 状态码到枚举的映射 */
    private static final Map<Integer, SubscriptionStatus> CODE_TO_ENUM = new HashMap<>();
    /** 枚举到状态码的映射 */
    private static final Map<SubscriptionStatus, Integer> ENUM_TO_CODE = new HashMap<>();
    /** 状态码到显示名称的映射 */
    private static final Map<Integer, String> CODE_TO_DISPLAY_NAME = new HashMap<>();
    
    static {
        // 初始化映射关系
        CODE_TO_ENUM.put(ACTIVE_CODE, SubscriptionStatus.ACTIVE);
        CODE_TO_ENUM.put(EXPIRED_CODE, SubscriptionStatus.EXPIRED);
        CODE_TO_ENUM.put(CANCELED_CODE, SubscriptionStatus.CANCELED);
        
        ENUM_TO_CODE.put(SubscriptionStatus.ACTIVE, ACTIVE_CODE);
        ENUM_TO_CODE.put(SubscriptionStatus.EXPIRED, EXPIRED_CODE);
        ENUM_TO_CODE.put(SubscriptionStatus.CANCELED, CANCELED_CODE);
        
        CODE_TO_DISPLAY_NAME.put(ACTIVE_CODE, "激活");
        CODE_TO_DISPLAY_NAME.put(EXPIRED_CODE, "过期");
        CODE_TO_DISPLAY_NAME.put(CANCELED_CODE, "取消");
    }
    
    /**
     * 根据状态码获取对应的枚举值
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static SubscriptionStatus fromCode(Integer code) {
        return CODE_TO_ENUM.get(code);
    }
    
    /**
     * 根据枚举值获取对应的状态码
     * 
     * @param status 枚举值
     * @return 对应的状态码，如果不存在则返回null
     */
    public static Integer toCode(SubscriptionStatus status) {
        return ENUM_TO_CODE.get(status);
    }
    
    /**
     * 根据状态码获取显示名称
     * 
     * @param code 状态码
     * @return 显示名称，如果不存在则返回null
     */
    public static String getDisplayName(Integer code) {
        return CODE_TO_DISPLAY_NAME.get(code);
    }
    
    /**
     * 根据枚举值获取显示名称
     * 
     * @param status 枚举值
     * @return 显示名称，如果不存在则返回null
     */
    public static String getDisplayName(SubscriptionStatus status) {
        Integer code = toCode(status);
        return code != null ? getDisplayName(code) : null;
    }
    
    /**
     * 检查状态码是否有效
     * 
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return CODE_TO_ENUM.containsKey(code);
    }
    
    /**
     * 检查状态是否为激活状态
     * 
     * @param code 状态码
     * @return 是否为激活状态
     */
    public static boolean isActive(Integer code) {
        return ACTIVE_CODE == code;
    }
    
    /**
     * 检查状态是否已过期
     * 
     * @param code 状态码
     * @return 是否已过期
     */
    public static boolean isExpired(Integer code) {
        return EXPIRED_CODE == code;
    }
    
    /**
     * 检查状态是否已取消
     * 
     * @param code 状态码
     * @return 是否已取消
     */
    public static boolean isCanceled(Integer code) {
        return CANCELED_CODE == code;
    }
}
