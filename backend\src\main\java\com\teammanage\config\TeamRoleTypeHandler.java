package com.teammanage.config;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import com.teammanage.enums.TeamRole;

/**
 * MyBatis TypeHandler for TeamRole enum
 * 
 * 处理数据库中的整数值与 TeamRole 枚举之间的转换
 * 使用权限级别作为数据库存储值
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@MappedTypes(TeamRole.class)
@MappedJdbcTypes(JdbcType.INTEGER)
public class TeamRoleTypeHandler extends BaseTypeHandler<TeamRole> {
    
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, TeamRole parameter, JdbcType jdbcType) throws SQLException {
        // 将枚举转换为权限级别存储到数据库
        ps.setInt(i, parameter.getPermissionLevel());
    }
    
    @Override
    public TeamRole getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int code = rs.getInt(columnName);
        return rs.wasNull() ? null : fromPermissionLevel(code);
    }
    
    @Override
    public TeamRole getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int code = rs.getInt(columnIndex);
        return rs.wasNull() ? null : fromPermissionLevel(code);
    }
    
    @Override
    public TeamRole getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int code = cs.getInt(columnIndex);
        return cs.wasNull() ? null : fromPermissionLevel(code);
    }
    
    /**
     * 根据权限级别获取对应的枚举值
     * 
     * @param permissionLevel 权限级别
     * @return 对应的枚举值，如果不存在则返回 null
     */
    private TeamRole fromPermissionLevel(int permissionLevel) {
        for (TeamRole role : TeamRole.values()) {
            if (role.getPermissionLevel() == permissionLevel) {
                return role;
            }
        }
        return null;
    }
}
