package com.teammanage.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * TODO实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("todos")
public class Todo extends BaseEntity {

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    @Size(max = 255, message = "任务标题长度不能超过255个字符")
    private String title;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 完成状态：0-未完成，1-已完成
     */
    @NotNull(message = "完成状态不能为空")
    @Min(value = 0, message = "状态值不能小于0")
    @Max(value = 1, message = "状态值不能大于1")
    private Integer status;

    /**
     * 优先级：1-低，2-中，3-高
     */
    @NotNull(message = "优先级不能为空")
    @Min(value = 1, message = "优先级不能小于1")
    @Max(value = 3, message = "优先级不能大于3")
    private Integer priority;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

}
