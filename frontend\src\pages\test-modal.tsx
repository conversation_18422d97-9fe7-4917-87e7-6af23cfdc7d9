import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import React, { useState } from 'react';

const TestModal: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);

  console.log('TestModal渲染，modalVisible:', modalVisible);

  return (
    <div style={{ padding: 20 }}>
      <h1>测试模态框</h1>
      
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => {
          console.log('点击按钮，设置modalVisible为true');
          setModalVisible(true);
        }}
      >
        打开模态框
      </Button>

      <ModalForm
        title="测试模态框"
        open={modalVisible}
        onOpenChange={(visible) => {
          console.log('onOpenChange:', visible);
          setModalVisible(visible);
        }}
        onFinish={async (values) => {
          console.log('提交表单:', values);
          return true;
        }}
      >
        <ProFormText
          name="test"
          label="测试字段"
          placeholder="请输入测试内容"
          rules={[{ required: true, message: '请输入测试内容' }]}
        />
      </ModalForm>
    </div>
  );
};

export default TestModal;
