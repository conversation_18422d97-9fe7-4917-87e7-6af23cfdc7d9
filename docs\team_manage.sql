/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3306
 Source Server Type    : MariaDB
 Source Server Version : 110402 (11.4.2-MariaDB)
 Source Host           : localhost:3306
 Source Schema         : team_manage

 Target Server Type    : MariaDB
 Target Server Version : 110402 (11.4.2-MariaDB)
 File Encoding         : 65001

 Date: 05/08/2025 14:58:58
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for account
-- ----------------------------
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '密码哈希',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户名',
  `default_subscription_plan_id` bigint(20) NULL DEFAULT NULL COMMENT '当前套餐ID',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '注册时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `telephone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `email`(`email`) USING BTREE,
  INDEX `idx_email`(`email`) USING BTREE,
  INDEX `idx_subscription_plan`(`default_subscription_plan_id`) USING BTREE,
  CONSTRAINT `account_ibfk_1` FOREIGN KEY (`default_subscription_plan_id`) REFERENCES `subscription_plan` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 92 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户账户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account
-- ----------------------------
INSERT INTO `account` VALUES (1, '<EMAIL>', '$2a$10$q9mxyE6XRqdsyElqwTZ38Oe5Cki8nzn7UVFPGtQNH9VJqg1JFyk5S', 'admin', 1, '2025-07-23 10:06:57', '2025-07-23 10:06:57', NULL);
INSERT INTO `account` VALUES (2, '<EMAIL>', '$2a$10$vNb8yNN9lVFJsyKqIC3K2OO7LlFWaVdovO78pcp3g9ZskOR8/QrBe', '更新后的用户名', 1, '2025-07-23 10:20:55', '2025-07-23 10:20:55', NULL);
INSERT INTO `account` VALUES (3, '<EMAIL>', '$2a$10$ChCYtV3VyqafUlT5eipK8OMYOIURiAZZZ370krAqC/ToTvE.1I9RW', '更新后的用户名', 1, '2025-07-23 10:24:38', '2025-07-23 10:24:38', NULL);
INSERT INTO `account` VALUES (4, '<EMAIL>', '$2a$10$ADF3FotUaqYAzknOh2GCYe/ggGuLMW/yEuMSHtPGRTqcTSAzC8gMi', '????3', 1, '2025-07-23 10:25:38', '2025-07-23 10:25:38', NULL);
INSERT INTO `account` VALUES (5, '<EMAIL>', '$2a$10$mbuY0NIlJ3zI0kMc7QAVYuBxRNni9csBJezx4uQ.afwn/WEKQ7Om.', '更新后的用户名', 1, '2025-07-23 10:26:33', '2025-07-23 10:26:33', NULL);
INSERT INTO `account` VALUES (6, '<EMAIL>', '$2a$10$upqnJlqlprFh8fej11vRJujmTRBS15hx6XaAawJaMbqwVD6iioAdK', '更新后的完整测试用户', 1, '2025-07-23 10:29:49', '2025-07-23 10:29:49', NULL);
INSERT INTO `account` VALUES (7, '<EMAIL>', '$2a$10$8jRMetPcuZ3o31O/x/hyuu7mfQ85ZhiZjBIZcKwFkcbmj2vxgsite', '更新后的完整测试用户b3u1w4', 1, '2025-07-23 10:33:37', '2025-07-23 10:33:37', NULL);
INSERT INTO `account` VALUES (8, '<EMAIL>', '$2a$10$qCWKAIBOyh7.pmLSFmbF9uDyIm9J8pMA4GfzTfEsTUabYlAUF.bM2', 'zhangsan', 2, '2025-07-23 12:07:35', '2025-07-27 19:50:03', '123456');
INSERT INTO `account` VALUES (9, '<EMAIL>', '$2a$10$Xk0or1fNmwlbGwIRgmKZjO06ab8XBUNHaFhH/ZqUomFr8xTsuwDtu', 'Test User', 1, '2025-07-23 13:58:42', '2025-07-23 13:58:42', NULL);
INSERT INTO `account` VALUES (10, '<EMAIL>', '$2a$10$J2rmSstteL8o2jgLgiJbOuPc4SaQ6EizNHS9cGXAiGzBxjgFUOjim', 'Test User', 1, '2025-07-23 14:03:11', '2025-07-23 14:03:11', NULL);
INSERT INTO `account` VALUES (11, '<EMAIL>', '$2a$10$t3P6d/pf7n8xFc53l873YuUNKplVJdN7ImrwX56DnXLPMfZ40uIdS', 'testuser', 1, '2025-07-23 14:16:00', '2025-07-23 14:16:00', NULL);
INSERT INTO `account` VALUES (12, '<EMAIL>', '$2a$10$ZFfHTQ8/qe1tTwaIQ7yz..kv0zCyBS1RrZaQHL/NdWiuMtB1QTqPG', 'testuser_n5ncrn4q', 1, '2025-07-23 14:18:38', '2025-07-23 14:18:38', NULL);
INSERT INTO `account` VALUES (13, '<EMAIL>', '$2a$10$lqoXb.HehtjUD0KZIPGv2uN7.VVfQcBCbyRZV4NrN8uoT3aRp9Mfa', 'testuser_l426tcbz', 1, '2025-07-23 14:19:18', '2025-07-23 14:19:18', NULL);
INSERT INTO `account` VALUES (14, '<EMAIL>', '$2a$10$3w4BwV6ggAsfher0T8vYau8b452rIbCwjdcGHrF9Ak5/644601dKi', 'Updated Test User', 1, '2025-07-24 16:32:03', '2025-07-24 16:32:03', NULL);
INSERT INTO `account` VALUES (15, '<EMAIL>', '$2a$10$Vn.lu6xu5sz27.rt6WSh2ujBbgmdiH6InSoPBpKCfbf68XZMfS58G', 'Test User 2', 1, '2025-07-24 16:32:04', '2025-07-24 16:32:04', NULL);
INSERT INTO `account` VALUES (16, '<EMAIL>', '$2a$10$G8ltgm.we9mgmYFmQmqGQ.yiXZ5JdSD6TLjoMPYhf83VgL.nbRqc.', 'Test User 3', 1, '2025-07-24 16:32:04', '2025-07-24 16:32:04', NULL);
INSERT INTO `account` VALUES (17, '<EMAIL>', '$2a$10$dr20qU4psZoTw.yP.gMi6uAEgt43i3SrhVuV/zM6tPH0WozjvOMwi', 'Debug User', 1, '2025-07-24 16:37:47', '2025-07-24 16:37:47', NULL);
INSERT INTO `account` VALUES (18, '<EMAIL>', '$2a$10$WelhEKl14nP.2i2CNWsq8uS0.ZgAbq/fgKhULh3NcHTP/Il3uCh36', 'Test User', 1, '2025-07-24 17:33:42', '2025-07-24 17:33:42', NULL);
INSERT INTO `account` VALUES (19, '<EMAIL>', '$2a$10$aJtJsqpWYhyRBD2WeXfBJOAFKivry/FWijvzrjput6Syg6bZe8lVa', '测试用户1', 1, '2025-07-25 09:40:19', '2025-07-25 09:40:19', NULL);
INSERT INTO `account` VALUES (20, '<EMAIL>', '$2a$10$XhghDdgpIGABuVFnac.TmuFS18ww9K4S740HepE3nbO9u0v2B7kKC', '测试用户2', 1, '2025-07-25 09:40:19', '2025-07-25 09:40:19', NULL);
INSERT INTO `account` VALUES (21, '<EMAIL>', '$2a$10$eCKUx2DsgcYq6unnVnmxGuymu69ujT.XvF6m6jrw2yToacxXTiEoy', '测试用户3', 1, '2025-07-25 09:40:20', '2025-07-25 09:40:20', NULL);
INSERT INTO `account` VALUES (22, '<EMAIL>', '$2a$10$mmpTgyR5uFv9y.HsRrqsx.8o4b73HKql6QUYmGbQBTupFqHh69iRu', '测试用户1', 1, '2025-07-25 10:01:28', '2025-07-25 10:01:28', NULL);
INSERT INTO `account` VALUES (23, '<EMAIL>', '$2a$10$1NIrTLUZmxRlqTgCXjj7Ge7qU9FXEWWKm0u/F.CvL5K1wZkWObgte', '测试用户2', 1, '2025-07-25 10:01:28', '2025-07-25 10:01:28', NULL);
INSERT INTO `account` VALUES (24, '<EMAIL>', '$2a$10$1mp71unxUfwWG1DOAWn6DegtWSKsWrN7WdwH633qywjUko4y2MQs2', '测试用户1', 1, '2025-07-25 10:03:40', '2025-07-25 10:03:40', NULL);
INSERT INTO `account` VALUES (25, '<EMAIL>', '$2a$10$tQwUqpdn/NIoLd6FouRrk.3.7rhmHFDGz3nKP1Lo5LYirb9ev1tHy', '测试用户2', 1, '2025-07-25 10:03:41', '2025-07-25 10:03:41', NULL);
INSERT INTO `account` VALUES (26, '<EMAIL>', '$2a$10$Nb/GP.p.QYDKE25Y40t.p.eq1HgrF5nyiKSndxnIeBeDv/V6FTOTW', '测试用户3', 1, '2025-07-25 10:03:41', '2025-07-25 10:03:41', NULL);
INSERT INTO `account` VALUES (27, '<EMAIL>', '$2a$10$/yv.68lzRF6aZ5RHz.Gj0uA3ZlL9G1u0B.fBO/HYtaQGo2BnEZP1y', '测试用户1', 1, '2025-07-25 10:04:33', '2025-07-25 10:04:33', NULL);
INSERT INTO `account` VALUES (28, '<EMAIL>', '$2a$10$XY/sjVhNdvvoAsVe2FVfZuMKPYsCoiSYfC2ALYmQb5X9nUPW0.BpW', '测试用户2', 1, '2025-07-25 10:04:34', '2025-07-25 10:04:34', NULL);
INSERT INTO `account` VALUES (29, '<EMAIL>', '$2a$10$81O3bRb/30DX//YVmcAJaORI2C0LY.usMBoBLFFymsYCtuUnsYu5y', '拒绝测试用户1', 1, '2025-07-25 10:05:19', '2025-07-25 10:05:19', NULL);
INSERT INTO `account` VALUES (30, '<EMAIL>', '$2a$10$7B5U8KuauNrRPbQD7vBZ8.zussjdO4GDoE9RdEYlbIWKgxdOVKETy', '拒绝测试用户2', 1, '2025-07-25 10:05:19', '2025-07-25 10:05:19', NULL);
INSERT INTO `account` VALUES (31, '<EMAIL>', '$2a$10$JLIff6Hs6wvJbVsoSntH4uQSDUpJGMYBvTKtH4sfZ4ugEafwPlSfm', '前端测试用户1', 1, '2025-07-25 10:18:07', '2025-07-25 10:18:07', NULL);
INSERT INTO `account` VALUES (32, '<EMAIL>', '$2a$10$6IReIxQGR6k9uQ6wk3/4nepp3jZEBQdsq5vvGdhgsEeldkGGh.yEi', '前端测试用户2', 1, '2025-07-25 10:18:07', '2025-07-25 10:18:07', NULL);
INSERT INTO `account` VALUES (33, '<EMAIL>', '$2a$10$4r/.NozpppaEXrMuF4fPHetfRHPk/WCDbCwnvY5i.oDPosZ7VtDr6', '前端测试用户1', 1, '2025-07-25 10:19:19', '2025-07-25 10:19:19', NULL);
INSERT INTO `account` VALUES (34, '<EMAIL>', '$2a$10$34YoRY2j/53FbgVAiaQKbe8H/UnsAVkvn0KzR.cITHOtKwZrdDEsW', '前端测试用户2', 1, '2025-07-25 10:19:20', '2025-07-25 10:19:20', NULL);
INSERT INTO `account` VALUES (35, '<EMAIL>', '$2a$10$WUStRJbT0WblWAVgiAgclOkm9hAgcNKmJyS5DGo3Cj.3aALW9EtLu', '核心测试用户1', 1, '2025-07-25 10:20:28', '2025-07-25 10:20:28', NULL);
INSERT INTO `account` VALUES (36, '<EMAIL>', '$2a$10$KhnrSqWsY0djtMtXlQaWjuUK6QGj7/FEjFIMTaHY/MDh1XY3499vO', '核心测试用户2', 1, '2025-07-25 10:20:28', '2025-07-25 10:20:28', NULL);
INSERT INTO `account` VALUES (75, '<EMAIL>', '$2a$10$/0VV.oNnfVHTdMrOKEC.8uAg6Nsp23yy1pKLdtbPIzm7lJVo7C1Iu', 'Test User', 1, '2025-07-30 00:02:56', '2025-07-30 00:02:56', NULL);
INSERT INTO `account` VALUES (76, '<EMAIL>', '$2a$10$Knmo1qYLIY2ISLaSvYk.VeuFasnMWUPeEYRwCWN3j5cWMKs4H/bI2', 'Test User', 1, '2025-07-30 00:20:09', '2025-07-30 00:20:09', NULL);
INSERT INTO `account` VALUES (77, '<EMAIL>', '$2a$10$esbtRRnssq9UrSDypXu24uIlNOvMpXqJpXXiCdAN0dXJapaKO0ZGK', 'Test User', 1, '2025-07-30 00:49:14', '2025-07-30 00:49:14', NULL);
INSERT INTO `account` VALUES (78, '<EMAIL>', '$2a$10$E5LvRVN.vUEgcAFSQ.vlVuF3Dmdos43GQEzMuJnEiPEKb.oKgqJlS', 'Test User', 1, '2025-07-30 00:50:19', '2025-07-30 00:50:19', NULL);
INSERT INTO `account` VALUES (79, '<EMAIL>', '$2a$10$oHoql359V9/ErfTckiw8Nuxls3vdLjZGVIveHoi1X27UjG3vHbWcC', 'Test User', 1, '2025-07-30 00:50:57', '2025-07-30 00:50:57', NULL);
INSERT INTO `account` VALUES (80, '<EMAIL>', '$2a$10$vVUsJ2jAztexDwf.tVqC0eyX8rWAb/Li3A43zJu00TvfIdVXkeHje', 'Test User', 1, '2025-07-30 09:43:38', '2025-07-30 09:43:38', NULL);
INSERT INTO `account` VALUES (81, '<EMAIL>', '$2a$10$Bpk9kQ7nMtIzBuG9Yz/5A.AuHAAq4V4EX9rSyAGnuF.kx9E1EZLTy', 'Test User Name', 1, '2025-07-30 09:43:38', '2025-07-30 09:43:38', NULL);
INSERT INTO `account` VALUES (82, '<EMAIL>', '$2a$10$bwi7mo.AuYBrpZAVwYxeO.TMLLtvdJNI8X9kIkSu8irP/00m/pAni', 'Test User', 1, '2025-07-30 09:44:09', '2025-07-30 09:44:09', NULL);
INSERT INTO `account` VALUES (83, '<EMAIL>', '$2a$10$tww.lglHWhWGsM1ytx3VIukLJPPxfdZhhn2b4BAdHFV.d35s4ZAoe', 'Test User Name', 1, '2025-07-30 09:44:09', '2025-07-30 09:44:09', NULL);
INSERT INTO `account` VALUES (84, '<EMAIL>', '$2a$10$4DuaS1o51R4g/fyFSL.hPeuiC/xUvgtcPmGv1U04joVoB8sf7gxF.', 'Test User *********', 1, '2025-07-30 09:53:49', '2025-07-30 09:53:49', NULL);
INSERT INTO `account` VALUES (85, '<EMAIL>', NULL, '13', 1, '2025-07-30 15:55:16', '2025-07-30 15:55:16', NULL);
INSERT INTO `account` VALUES (86, '<EMAIL>', NULL, '11', 1, '2025-07-30 16:25:18', '2025-07-30 16:25:18', NULL);
INSERT INTO `account` VALUES (87, '<EMAIL>', NULL, 'c', 1, '2025-07-30 16:26:19', '2025-07-30 16:26:19', NULL);
INSERT INTO `account` VALUES (88, '<EMAIL>', NULL, 'gg', 1, '2025-07-30 16:28:03', '2025-07-30 16:28:03', NULL);
INSERT INTO `account` VALUES (89, '<EMAIL>', NULL, 'x', 1, '2025-07-30 16:35:14', '2025-07-30 16:35:14', NULL);
INSERT INTO `account` VALUES (90, '<EMAIL>', NULL, 'h', 1, '2025-07-30 16:45:03', '2025-07-30 16:45:03', NULL);
INSERT INTO `account` VALUES (91, '<EMAIL>', NULL, 'zzz', 1, '2025-07-30 18:45:20', '2025-07-30 18:45:20', NULL);



-- ----------------------------
-- Table structure for account_subscription
-- ----------------------------
DROP TABLE IF EXISTS `account_subscription`;
CREATE TABLE `account_subscription`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `account_id` bigint(20) NOT NULL COMMENT '用户ID',
  `subscription_plan_id` bigint(20) NOT NULL COMMENT '套餐ID',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NULL DEFAULT NULL COMMENT '结束日期',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '订阅状态：1=激活, 2=过期, 3=取消',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_account_status`(`account_id`, `status`) USING BTREE,
  INDEX `idx_date_range`(`start_date`, `end_date`) USING BTREE,
  INDEX `idx_plan`(`subscription_plan_id`) USING BTREE,
  CONSTRAINT `account_subscription_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `account_subscription_ibfk_2` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plan` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户订阅历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_subscription
-- ----------------------------
INSERT INTO `account_subscription` VALUES (1, 2, 1, '2025-07-23', '2025-08-23', 1, '2025-07-23 10:22:49', '2025-07-23 10:22:49');
INSERT INTO `account_subscription` VALUES (2, 3, 1, '2025-07-23', '2025-08-23', 1, '2025-07-23 10:24:42', '2025-07-23 10:24:42');
INSERT INTO `account_subscription` VALUES (3, 5, 1, '2025-07-23', '2025-08-23', 1, '2025-07-23 10:26:34', '2025-07-23 10:26:34');
INSERT INTO `account_subscription` VALUES (4, 6, 1, '2025-07-23', '2025-08-23', 3, '2025-07-23 10:29:50', '2025-07-23 10:29:50');
INSERT INTO `account_subscription` VALUES (5, 6, 1, '2025-07-23', '2025-08-23', 1, '2025-07-23 10:30:17', '2025-07-23 10:30:17');
INSERT INTO `account_subscription` VALUES (6, 7, 1, '2025-07-23', '2025-08-23', 1, '2025-07-23 10:33:37', '2025-07-23 10:33:37');
INSERT INTO `account_subscription` VALUES (7, 8, 2, '2025-07-23', '2025-08-23', 1, '2025-07-23 15:23:46', '2025-07-23 15:23:46');
INSERT INTO `account_subscription` VALUES (8, 14, 1, '2025-07-24', NULL, 3, '2025-07-24 16:36:33', '2025-07-24 16:36:33');
INSERT INTO `account_subscription` VALUES (9, 14, 1, '2025-07-24', NULL, 3, '2025-07-24 16:45:58', '2025-07-24 16:45:58');
INSERT INTO `account_subscription` VALUES (10, 14, 1, '2025-07-24', NULL, 3, '2025-07-24 17:00:10', '2025-07-24 17:00:10');
INSERT INTO `account_subscription` VALUES (11, 14, 1, '2025-07-24', NULL, 3, '2025-07-24 17:04:04', '2025-07-24 17:04:04');
INSERT INTO `account_subscription` VALUES (12, 14, 1, '2025-07-24', NULL, 3, '2025-07-24 17:23:42', '2025-07-24 17:23:42');

-- ----------------------------
-- Table structure for subscription_plan
-- ----------------------------
DROP TABLE IF EXISTS `subscription_plan`;
CREATE TABLE `subscription_plan`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '套餐ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '套餐名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '套餐说明',
  `max_size` int(11) NOT NULL COMMENT '数据数量上限',
  `price` decimal(10, 2) NOT NULL COMMENT '价格(元/月)',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_active`(`is_active`) USING BTREE,
  INDEX `idx_price`(`price`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品套餐表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of subscription_plan
-- ----------------------------
INSERT INTO `subscription_plan` VALUES (1, '免费版', '适合个人用户，可创建1个团队', 1, 0.00, 1, '2025-07-23 09:40:17', '2025-07-24 14:57:08');
INSERT INTO `subscription_plan` VALUES (2, '标准版', '适合小团队，可创建5个团队', 5, 99.00, 1, '2025-07-23 09:40:17', '2025-07-24 14:57:08');
INSERT INTO `subscription_plan` VALUES (3, '专业版', '适合中型企业，可创建20个团队', 20, 299.00, 1, '2025-07-23 09:40:17', '2025-07-24 14:57:08');
INSERT INTO `subscription_plan` VALUES (4, '企业版', '适合大型企业，可创建无限团队', 999999, 999.00, 1, '2025-07-23 09:40:17', '2025-07-24 14:57:08');
INSERT INTO `subscription_plan` VALUES (5, '免费版', '适合个人用户，可创建1个团队', 1, 0.00, 1, '2025-07-27 18:31:32', '2025-07-27 18:31:32');
INSERT INTO `subscription_plan` VALUES (6, '标准版', '适合小团队，可创建5个团队', 5, 99.00, 1, '2025-07-27 18:31:32', '2025-07-27 18:31:32');
INSERT INTO `subscription_plan` VALUES (7, '专业版', '适合中型企业，可创建20个团队', 20, 299.00, 1, '2025-07-27 18:31:32', '2025-07-27 18:31:32');
INSERT INTO `subscription_plan` VALUES (8, '企业版', '适合大型企业，可创建无限团队', 999999, 999.00, 1, '2025-07-27 18:31:32', '2025-07-27 18:31:32');

-- ----------------------------
-- Table structure for team
-- ----------------------------
DROP TABLE IF EXISTS `team`;
CREATE TABLE `team`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '团队ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '团队名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '团队描述',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_creator`(`created_by`, `is_deleted`) USING BTREE,
  INDEX `idx_name`(`name`) USING BTREE,
  CONSTRAINT `team_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `account` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '团队信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of team
-- ----------------------------
INSERT INTO `team` VALUES (2, 'xx', 'xx', 1, '2025-07-23 10:10:49', '2025-07-23 10:10:49', 0);
INSERT INTO `team` VALUES (3, '新测试团队', '这是一个新创建的测试团队', 2, '2025-07-23 10:22:48', '2025-07-23 10:22:48', 0);
INSERT INTO `team` VALUES (4, '更新后的团队名', '更新后的团队描述', 5, '2025-07-23 10:26:33', '2025-07-23 10:26:33', 0);
INSERT INTO `team` VALUES (5, '更新后的完整测试团队', '更新后的团队描述', 6, '2025-07-23 10:29:50', '2025-07-23 10:29:50', 0);
INSERT INTO `team` VALUES (6, '完整测试团队', '这是一个完整API测试创建的团队', 6, '2025-07-23 10:30:17', '2025-07-23 10:30:17', 0);
INSERT INTO `team` VALUES (7, '更新后的完整测试团队b3u1w4', '更新后的团队描述b3u1w4', 7, '2025-07-23 10:33:37', '2025-07-23 10:33:37', 0);
INSERT INTO `team` VALUES (8, '更新后的完整测试团队b3u1w4', '123', 8, '2025-07-23 12:07:51', '2025-08-01 16:17:26', 0);
INSERT INTO `team` VALUES (9, '测试团队_1753250591', 'API测试创建的团队', 10, '2025-07-23 14:03:11', '2025-07-23 14:03:11', 0);
INSERT INTO `team` VALUES (10, '测试团队', '这是一个测试团队', 11, '2025-07-23 14:16:01', '2025-07-23 14:16:01', 0);
INSERT INTO `team` VALUES (11, '测试团队_s33rtr', '这是一个测试团队_s33rtr', 13, '2025-07-23 14:19:18', '2025-07-23 14:19:18', 0);
INSERT INTO `team` VALUES (12, '1233', NULL, 8, '2025-07-23 15:24:33', '2025-07-23 15:24:33', 0);
INSERT INTO `team` VALUES (13, 'Updated Test Team', 'Updated description', 14, '2025-07-24 16:36:33', '2025-07-24 16:36:33', 0);
INSERT INTO `team` VALUES (14, 'Test Team 1', 'A test team created by API test', 17, '2025-07-24 17:06:37', '2025-07-24 17:06:37', 0);
INSERT INTO `team` VALUES (24, '测试团队_1753804976', '这是一个用于 API 测试的团队', 75, '2025-07-30 00:02:56', '2025-07-30 00:02:56', 0);
INSERT INTO `team` VALUES (25, '测试团队_1753806009', '这是一个用于 API 测试的团队', 76, '2025-07-30 00:20:09', '2025-07-30 00:20:09', 0);
INSERT INTO `team` VALUES (26, 'Test Team f792ae53', 'This is a test team created by automated testing', 80, '2025-07-30 09:43:39', '2025-07-30 09:43:39', 0);
INSERT INTO `team` VALUES (27, 'Test Team 1705606956', 'A test team created by API testing script', 84, '2025-07-30 09:53:50', '2025-07-30 09:53:50', 0);
INSERT INTO `team` VALUES (28, '222', NULL, 91, '2025-07-30 18:48:15', '2025-07-30 18:48:15', 0);

-- ----------------------------
-- Table structure for team_invitation
-- ----------------------------
DROP TABLE IF EXISTS `team_invitation`;
CREATE TABLE `team_invitation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '邀请ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID',
  `inviter_id` bigint(20) NOT NULL COMMENT '邀请人ID',
  `invitee_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '被邀请人邮箱',
  `invitee_id` bigint(20) NULL DEFAULT NULL COMMENT '被邀请人ID（邀请时可能为空）',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '邀请状态：1=待确认, 2=已确认, 3=已拒绝, 4=已过期, 5=已取消',
  `invited_at` timestamp NULL DEFAULT current_timestamp() COMMENT '邀请时间',
  `responded_at` timestamp NULL DEFAULT NULL COMMENT '响应时间',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邀请消息',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_team_status`(`team_id`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '团队邀请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of team_invitation
-- ----------------------------
INSERT INTO `team_invitation` VALUES (1, 1, 1, '<EMAIL>', NULL, 4, '2025-07-29 21:39:05', NULL, '2025-08-01 21:39:05', '欢迎加入我们的团队！', '2025-07-29 21:39:05', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (2, 1, 1, '<EMAIL>', NULL, 4, '2025-07-29 21:39:05', NULL, '2025-08-01 21:39:05', '期待与您一起工作。', '2025-07-29 21:39:05', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (6, 8, 8, '<EMAIL>', 2, 2, '2025-07-30 09:56:50', NULL, '2025-08-02 09:56:50', NULL, '2025-07-30 09:56:50', '2025-07-30 09:58:26');
INSERT INTO `team_invitation` VALUES (7, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 13:17:44', NULL, '2025-08-02 13:17:44', NULL, '2025-07-30 13:17:44', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (8, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 13:35:37', NULL, '2025-08-02 13:35:37', NULL, '2025-07-30 13:35:37', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (9, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 13:38:36', NULL, '2025-08-02 13:38:36', NULL, '2025-07-30 13:38:36', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (10, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 14:34:03', NULL, '2025-08-02 14:34:03', NULL, '2025-07-30 14:34:03', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (11, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 14:45:03', NULL, '2025-08-02 14:45:03', NULL, '2025-07-30 14:45:03', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (12, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 15:10:01', NULL, '2025-08-02 15:10:01', NULL, '2025-07-30 15:10:01', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (13, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 15:21:06', NULL, '2025-08-02 15:21:06', NULL, '2025-07-30 15:21:06', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (14, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 15:26:38', NULL, '2025-08-02 15:26:38', NULL, '2025-07-30 15:26:38', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (15, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 15:28:49', NULL, '2025-08-02 15:28:49', NULL, '2025-07-30 15:28:49', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (16, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 15:30:36', NULL, '2025-08-02 15:30:36', NULL, '2025-07-30 15:30:36', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (17, 8, 8, '<EMAIL>', NULL, 4, '2025-07-30 15:44:19', NULL, '2025-08-02 15:44:19', NULL, '2025-07-30 15:44:19', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (18, 12, 8, '<EMAIL>', NULL, 4, '2025-07-30 15:49:03', NULL, '2025-08-02 15:49:03', NULL, '2025-07-30 15:49:03', '2025-08-04 12:09:26');
INSERT INTO `team_invitation` VALUES (19, 8, 8, '<EMAIL>', 85, 2, '2025-07-30 15:53:55', '2025-07-30 15:55:16', '2025-08-02 15:53:55', NULL, '2025-07-30 15:53:55', '2025-07-30 15:53:55');
INSERT INTO `team_invitation` VALUES (20, 12, 8, '<EMAIL>', 87, 2, '2025-07-30 16:26:06', '2025-07-30 16:26:19', '2025-08-02 16:26:06', NULL, '2025-07-30 16:26:06', '2025-07-30 16:26:06');
INSERT INTO `team_invitation` VALUES (21, 12, 8, '<EMAIL>', 88, 2, '2025-07-30 16:27:29', '2025-07-30 16:28:03', '2025-08-02 16:27:29', NULL, '2025-07-30 16:27:29', '2025-07-30 16:27:29');
INSERT INTO `team_invitation` VALUES (22, 12, 8, '<EMAIL>', 89, 2, '2025-07-30 16:35:04', '2025-07-30 16:35:14', '2025-08-02 16:35:04', NULL, '2025-07-30 16:35:04', '2025-07-30 16:35:04');
INSERT INTO `team_invitation` VALUES (23, 12, 8, '<EMAIL>', 90, 2, '2025-07-30 16:44:52', '2025-07-30 16:45:03', '2025-08-02 16:44:52', NULL, '2025-07-30 16:44:52', '2025-07-30 16:44:52');
INSERT INTO `team_invitation` VALUES (24, 12, 8, '<EMAIL>', 91, 2, '2025-07-30 18:45:10', '2025-07-30 18:45:20', '2025-08-02 18:45:10', NULL, '2025-07-30 18:45:10', '2025-07-30 18:45:10');

-- ----------------------------
-- Table structure for team_member
-- ----------------------------
DROP TABLE IF EXISTS `team_member`;
CREATE TABLE `team_member`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '团队成员ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID',
  `account_id` bigint(20) NOT NULL COMMENT '用户ID',
  `is_creator` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为创建者',
  `role` int(11) NOT NULL DEFAULT 10 COMMENT '团队角色：100=团队创建者, 10=团队成员',
  `assigned_at` timestamp NULL DEFAULT current_timestamp() COMMENT '分配时间',
  `last_access_time` timestamp NULL DEFAULT NULL COMMENT '最后访问时间',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '账号状态',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_team_member`(`team_id`, `account_id`, `is_deleted`) USING BTREE,
  INDEX `idx_team_active`(`team_id`, `is_active`, `is_deleted`) USING BTREE,
  INDEX `idx_account_team`(`account_id`, `team_id`, `is_deleted`) USING BTREE,
  INDEX `idx_creator`(`team_id`, `is_creator`, `is_deleted`) USING BTREE,
  INDEX `idx_last_access`(`account_id`, `last_access_time`) USING BTREE,
  INDEX `idx_team_member_role`(`role`) USING BTREE,
  CONSTRAINT `team_member_ibfk_1` FOREIGN KEY (`team_id`) REFERENCES `team` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `team_member_ibfk_2` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '团队成员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of team_member
-- ----------------------------
INSERT INTO `team_member` VALUES (1, 2, 1, 1, 100, '2025-07-23 10:10:49', '2025-07-23 10:10:49', 1, 0, '2025-07-29 22:22:34', '2025-07-23 10:10:49');
INSERT INTO `team_member` VALUES (2, 3, 2, 1, 100, '2025-07-23 10:22:48', '2025-07-23 10:22:48', 1, 0, '2025-07-29 22:22:34', '2025-07-23 10:22:48');
INSERT INTO `team_member` VALUES (3, 4, 5, 1, 100, '2025-07-23 10:26:33', '2025-07-23 10:26:33', 1, 0, '2025-07-29 22:22:34', '2025-07-23 10:26:33');
INSERT INTO `team_member` VALUES (4, 5, 6, 1, 100, '2025-07-23 10:29:50', '2025-07-23 10:29:50', 1, 0, '2025-07-29 22:22:34', '2025-07-23 10:29:50');
INSERT INTO `team_member` VALUES (5, 6, 6, 1, 100, '2025-07-23 10:30:17', '2025-07-23 10:30:17', 1, 0, '2025-07-29 22:22:34', '2025-07-23 10:30:17');
INSERT INTO `team_member` VALUES (6, 7, 7, 1, 100, '2025-07-23 10:33:37', '2025-07-23 10:33:38', 1, 0, '2025-07-29 22:22:34', '2025-07-23 10:33:37');
INSERT INTO `team_member` VALUES (7, 8, 8, 1, 100, '2025-07-23 12:07:51', '2025-08-05 14:44:57', 1, 0, '2025-07-29 22:22:34', '2025-07-23 12:07:51');
INSERT INTO `team_member` VALUES (8, 9, 10, 1, 100, '2025-07-23 14:03:11', '2025-07-23 14:03:11', 1, 0, '2025-07-29 22:22:34', '2025-07-23 14:03:11');
INSERT INTO `team_member` VALUES (9, 10, 11, 1, 100, '2025-07-23 14:16:01', '2025-07-23 14:16:01', 1, 0, '2025-07-29 22:22:34', '2025-07-23 14:16:01');
INSERT INTO `team_member` VALUES (10, 11, 13, 1, 100, '2025-07-23 14:19:18', '2025-07-23 14:19:19', 1, 0, '2025-07-29 22:22:34', '2025-07-23 14:19:18');
INSERT INTO `team_member` VALUES (11, 12, 8, 1, 100, '2025-07-23 15:24:33', '2025-08-01 18:28:04', 1, 0, '2025-07-29 22:22:34', '2025-07-23 15:24:33');
INSERT INTO `team_member` VALUES (12, 13, 14, 1, 100, '2025-07-24 16:36:33', '2025-07-24 16:36:33', 1, 0, '2025-07-29 22:22:34', '2025-07-24 16:36:33');
INSERT INTO `team_member` VALUES (13, 14, 17, 1, 100, '2025-07-24 17:06:37', '2025-07-24 17:06:37', 1, 0, '2025-07-29 22:22:34', '2025-07-24 17:06:37');
INSERT INTO `team_member` VALUES (29, 24, 75, 1, 100, '2025-07-30 00:02:56', '2025-07-30 00:02:56', 1, 0, '2025-07-30 00:02:56', '2025-07-30 00:02:56');
INSERT INTO `team_member` VALUES (30, 25, 76, 1, 100, '2025-07-30 00:20:09', '2025-07-30 00:20:09', 1, 0, '2025-07-30 00:20:09', '2025-07-30 00:20:09');
INSERT INTO `team_member` VALUES (31, 26, 80, 1, 100, '2025-07-30 09:43:39', '2025-07-30 09:43:39', 1, 0, '2025-07-30 09:43:39', '2025-07-30 09:43:39');
INSERT INTO `team_member` VALUES (32, 27, 84, 1, 100, '2025-07-30 09:53:50', '2025-07-30 09:53:50', 1, 0, '2025-07-30 09:53:50', '2025-07-30 09:53:50');
INSERT INTO `team_member` VALUES (33, 8, 85, 0, 10, '2025-07-30 15:55:16', NULL, 0, 0, '2025-07-30 15:55:16', '2025-07-30 15:55:16');
INSERT INTO `team_member` VALUES (34, 12, 87, 0, 10, '2025-07-30 16:26:19', NULL, 0, 0, '2025-07-30 16:26:19', '2025-07-30 16:26:19');
INSERT INTO `team_member` VALUES (35, 12, 88, 0, 10, '2025-07-30 16:28:03', NULL, 0, 0, '2025-07-30 16:28:03', '2025-07-30 16:28:03');
INSERT INTO `team_member` VALUES (36, 12, 89, 0, 10, '2025-07-30 16:35:14', '2025-07-30 16:58:06', 0, 0, '2025-07-30 16:35:14', '2025-07-30 16:35:14');
INSERT INTO `team_member` VALUES (37, 12, 90, 0, 10, '2025-07-30 16:45:03', '2025-07-30 16:45:09', 0, 0, '2025-07-30 16:45:03', '2025-07-30 16:45:03');
INSERT INTO `team_member` VALUES (38, 12, 91, 0, 10, '2025-07-30 18:45:20', NULL, 0, 0, '2025-07-30 18:45:20', '2025-07-30 18:45:20');
INSERT INTO `team_member` VALUES (39, 28, 91, 1, 100, '2025-07-30 18:48:15', '2025-07-30 18:48:18', 1, 0, '2025-07-30 18:48:15', '2025-07-30 18:48:15');

-- ----------------------------
-- Table structure for todos
-- ----------------------------
DROP TABLE IF EXISTS `todos`;
CREATE TABLE `todos`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'TODO ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '任务描述',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '完成状态：0-未完成，1-已完成',
  `priority` tinyint(4) NOT NULL DEFAULT 2 COMMENT '优先级：1-低，2-中，3-高',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `created_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_priority`(`priority`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `todos_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `account` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'TODO表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of todos
-- ----------------------------
INSERT INTO `todos` VALUES (1, '完善个人资料信息', '更新个人基本信息和联系方式', 1, 1, 8, '2025-07-27 18:31:32', '2025-07-27 18:48:16');
INSERT INTO `todos` VALUES (2, '驾驶证信息完善', '上传驾驶证照片并填写相关信息', 1, 2, 8, '2025-07-27 18:31:32', '2025-07-27 18:48:16');
INSERT INTO `todos` VALUES (3, '设置提醒收藏', '配置重要事项的提醒功能', 1, 1, 8, '2025-07-27 18:31:32', '2025-07-27 18:48:16');
INSERT INTO `todos` VALUES (4, '车辆保养记录更新', '更新所有车辆的保养记录和下次保养时间', 1, 2, 8, '2025-07-27 18:31:32', '2025-08-04 15:29:53');
INSERT INTO `todos` VALUES (5, '团队会议准备', '准备下周团队会议的议程和材料', 0, 3, 8, '2025-07-27 18:31:32', '2025-07-27 18:48:16');
INSERT INTO `todos` VALUES (6, '车辆安全检查报告', '完成本月车辆安全检查并提交报告', 0, 3, 8, '2025-07-27 18:31:32', '2025-07-27 18:48:16');
INSERT INTO `todos` VALUES (7, 'Test TODO 1b69570a', 'This is a test TODO item', 0, 2, 80, '2025-07-30 09:43:39', '2025-07-30 09:43:39');

SET FOREIGN_KEY_CHECKS = 1;
