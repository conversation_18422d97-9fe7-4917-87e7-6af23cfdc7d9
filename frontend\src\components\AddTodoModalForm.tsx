import { PlusOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText, ProFormSelect } from '@ant-design/pro-components';
import { message } from 'antd';
import React from 'react';
import { TodoService } from '@/services/todo';
import type { CreateTodoRequest, TodoResponse } from '@/types/api';

interface AddTodoModalFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: (todo: TodoResponse) => void;
}

/**
 * 添加待办事项模态框组件
 */
const AddTodoModalForm: React.FC<AddTodoModalFormProps> = ({
  open,
  onOpenChange,
  onSuccess,
}) => {
  const handleSubmit = async (values: CreateTodoRequest) => {
    try {
      // 等待一小段时间以显示提交状态
      await new Promise(resolve => setTimeout(resolve, 500));

      const newTodo = await TodoService.createTodo({
        title: values.title,
        priority: values.priority,
      });

      message.success('任务创建成功！');
      onSuccess(newTodo);
      return true; // 返回true表示操作成功，ModalForm会自动关闭
    } catch (error) {
      console.error('创建待办事项失败:', error);
      return false; // 返回false表示操作失败，ModalForm不会关闭
    }
  };

  return (
    <ModalForm<CreateTodoRequest>
      title="新增待办事项"
      open={open}
      onOpenChange={onOpenChange}
      modalProps={{
        destroyOnClose: true,
        maskClosable: true,
      }}
      submitTimeout={2000}
      onFinish={handleSubmit}
      submitter={{
        searchConfig: {
          submitText: '创建任务',
          resetText: '取消',
        },
        submitButtonProps: {
          icon: <PlusOutlined />,
        },
      }}
    >
      <ProFormText
        name="title"
        label="任务名称"
        placeholder="请输入任务名称"
        rules={[
          { required: true, message: '请输入任务名称' },
          { max: 100, message: '任务名称不能超过100个字符' },
          { whitespace: true, message: '任务名称不能为空白字符' }
        ]}
      />

      <ProFormSelect
        name="priority"
        label="优先级"
        placeholder="请选择优先级"
        initialValue={2}
        rules={[{ required: true, message: '请选择优先级' }]}
        options={[
          { value: 3, label: '高优先级' },
          { value: 2, label: '中优先级' },
          { value: 1, label: '低优先级' },
        ]}
      />
    </ModalForm>
  );
};

export default AddTodoModalForm;
