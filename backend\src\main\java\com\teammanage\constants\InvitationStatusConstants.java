package com.teammanage.constants;

import com.teammanage.entity.TeamInvitation.InvitationStatus;
import java.util.HashMap;
import java.util.Map;

/**
 * 邀请状态常量类
 * 
 * 定义邀请状态的标识符和映射关系，支持状态标识符格式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class InvitationStatusConstants {
    
    /** 待确认状态码 */
    public static final int PENDING_CODE = 1;
    /** 已确认状态码 */
    public static final int ACCEPTED_CODE = 2;
    /** 已拒绝状态码 */
    public static final int REJECTED_CODE = 3;
    /** 已过期状态码 */
    public static final int EXPIRED_CODE = 4;
    /** 已取消状态码 */
    public static final int CANCELLED_CODE = 5;
    
    /** 状态码到枚举的映射 */
    private static final Map<Integer, InvitationStatus> CODE_TO_ENUM = new HashMap<>();
    /** 枚举到状态码的映射 */
    private static final Map<InvitationStatus, Integer> ENUM_TO_CODE = new HashMap<>();
    /** 状态码到显示名称的映射 */
    private static final Map<Integer, String> CODE_TO_DISPLAY_NAME = new HashMap<>();
    
    static {
        // 初始化映射关系
        CODE_TO_ENUM.put(PENDING_CODE, InvitationStatus.PENDING);
        CODE_TO_ENUM.put(ACCEPTED_CODE, InvitationStatus.ACCEPTED);
        CODE_TO_ENUM.put(REJECTED_CODE, InvitationStatus.REJECTED);
        CODE_TO_ENUM.put(EXPIRED_CODE, InvitationStatus.EXPIRED);
        CODE_TO_ENUM.put(CANCELLED_CODE, InvitationStatus.CANCELLED);
        
        ENUM_TO_CODE.put(InvitationStatus.PENDING, PENDING_CODE);
        ENUM_TO_CODE.put(InvitationStatus.ACCEPTED, ACCEPTED_CODE);
        ENUM_TO_CODE.put(InvitationStatus.REJECTED, REJECTED_CODE);
        ENUM_TO_CODE.put(InvitationStatus.EXPIRED, EXPIRED_CODE);
        ENUM_TO_CODE.put(InvitationStatus.CANCELLED, CANCELLED_CODE);
        
        CODE_TO_DISPLAY_NAME.put(PENDING_CODE, "待确认");
        CODE_TO_DISPLAY_NAME.put(ACCEPTED_CODE, "已确认");
        CODE_TO_DISPLAY_NAME.put(REJECTED_CODE, "已拒绝");
        CODE_TO_DISPLAY_NAME.put(EXPIRED_CODE, "已过期");
        CODE_TO_DISPLAY_NAME.put(CANCELLED_CODE, "已取消");
    }
    
    /**
     * 根据状态码获取对应的枚举值
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static InvitationStatus fromCode(Integer code) {
        return CODE_TO_ENUM.get(code);
    }
    
    /**
     * 根据枚举值获取对应的状态码
     * 
     * @param status 枚举值
     * @return 对应的状态码，如果不存在则返回null
     */
    public static Integer toCode(InvitationStatus status) {
        return ENUM_TO_CODE.get(status);
    }
    
    /**
     * 根据状态码获取显示名称
     * 
     * @param code 状态码
     * @return 显示名称，如果不存在则返回null
     */
    public static String getDisplayName(Integer code) {
        return CODE_TO_DISPLAY_NAME.get(code);
    }
    
    /**
     * 根据枚举值获取显示名称
     * 
     * @param status 枚举值
     * @return 显示名称，如果不存在则返回null
     */
    public static String getDisplayName(InvitationStatus status) {
        Integer code = toCode(status);
        return code != null ? getDisplayName(code) : null;
    }
    
    /**
     * 检查状态码是否有效
     * 
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return CODE_TO_ENUM.containsKey(code);
    }
    
    /**
     * 检查状态是否可以被响应（接受或拒绝）
     * 
     * @param code 状态码
     * @return 是否可以被响应
     */
    public static boolean canBeResponded(Integer code) {
        return PENDING_CODE == code;
    }
    
    /**
     * 检查状态是否可以被取消
     * 
     * @param code 状态码
     * @return 是否可以被取消
     */
    public static boolean canBeCancelled(Integer code) {
        return PENDING_CODE == code;
    }
    
    /**
     * 检查状态是否已过期
     * 
     * @param code 状态码
     * @return 是否已过期
     */
    public static boolean isExpired(Integer code) {
        return EXPIRED_CODE == code;
    }
}
