/**
 * 仪表板页面
 *
 * 企业级仪表板页面，提供数据概览、快速操作和系统状态监控。
 * 采用响应式设计，适配不同屏幕尺寸，使用专业商务配色方案。
 */

import { PageContainer, ProCard, StatisticCard } from '@ant-design/pro-components';
import { Row, Col, Typography, Space, Button, Divider } from 'antd';
import {
  DashboardOutlined,
  TeamOutlined,
  UserOutlined,
  SettingOutlined,
  BarChartOutlined,
  BellOutlined,
  FileTextOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import React from 'react';

const { Title, Text, Paragraph } = Typography;

const Dashboard: React.FC = () => {
  return (
    <PageContainer
      title="仪表板"
      subTitle="团队协作管理系统概览"
      extra={[
        <Button key="refresh" icon={<BarChartOutlined />}>
          刷新数据
        </Button>,
        <Button key="settings" icon={<SettingOutlined />} type="primary">
          系统设置
        </Button>,
      ]}
    >
      <div style={{ padding: '0 24px' }}>
        {/* 欢迎区域 */}
        <ProCard
          style={{
            marginBottom: 24,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            borderRadius: 16,
          }}
          bodyStyle={{ padding: '32px' }}
        >
          <Row gutter={[24, 24]} align="middle">
            <Col xs={24} sm={24} md={16} lg={18} xl={18} xxl={18}>
              <Space direction="vertical" size="small">
                <Title level={2} style={{ color: 'white', margin: 0 }}>
                  <DashboardOutlined style={{ marginRight: 12 }} />
                  欢迎使用团队协作管理系统
                </Title>
                <Paragraph style={{ color: 'rgba(255, 255, 255, 0.9)', margin: 0, fontSize: 16 }}>
                  高效管理团队，提升协作效率，让每个项目都能成功交付
                </Paragraph>
              </Space>
            </Col>
            <Col xs={24} sm={24} md={8} lg={6} xl={6} xxl={6}>
              <div style={{ textAlign: 'center' }}>
                <TrophyOutlined style={{ fontSize: 64, color: 'rgba(255, 255, 255, 0.8)' }} />
              </div>
            </Col>
          </Row>
        </ProCard>

        {/* 统计卡片区域 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={12} sm={12} md={6} lg={6} xl={6} xxl={6}>
            <StatisticCard
              statistic={{
                title: '团队数量',
                value: 8,
                icon: <TeamOutlined style={{ color: '#2563eb' }} />,
              }}
              style={{
                background: 'linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%)',
                borderRadius: 12,
              }}
            />
          </Col>
          <Col xs={12} sm={12} md={6} lg={6} xl={6} xxl={6}>
            <StatisticCard
              statistic={{
                title: '团队成员',
                value: 156,
                icon: <UserOutlined style={{ color: '#059669' }} />,
              }}
              style={{
                background: 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%)',
                borderRadius: 12,
              }}
            />
          </Col>
          <Col xs={12} sm={12} md={6} lg={6} xl={6} xxl={6}>
            <StatisticCard
              statistic={{
                title: '活跃项目',
                value: 24,
                icon: <FileTextOutlined style={{ color: '#d97706' }} />,
              }}
              style={{
                background: 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)',
                borderRadius: 12,
              }}
            />
          </Col>
          <Col xs={12} sm={12} md={6} lg={6} xl={6} xxl={6}>
            <StatisticCard
              statistic={{
                title: '待处理任务',
                value: 42,
                icon: <BellOutlined style={{ color: '#dc2626' }} />,
              }}
              style={{
                background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',
                borderRadius: 12,
              }}
            />
          </Col>
        </Row>

        {/* 主要功能区域 */}
        <Row gutter={[24, 24]}>
          {/* 快速操作 */}
          <Col xs={24} sm={24} md={12} lg={8} xl={8} xxl={8}>
            <ProCard
              title="快速操作"
              headerBordered
              style={{ height: '100%', borderRadius: 12 }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  icon={<TeamOutlined />}
                  size="large"
                  block
                  style={{ height: 48, borderRadius: 8 }}
                >
                  创建新团队
                </Button>
                <Button
                  icon={<UserOutlined />}
                  size="large"
                  block
                  style={{ height: 48, borderRadius: 8 }}
                >
                  邀请成员
                </Button>
                <Button
                  icon={<FileTextOutlined />}
                  size="large"
                  block
                  style={{ height: 48, borderRadius: 8 }}
                >
                  创建项目
                </Button>
                <Divider style={{ margin: '12px 0' }} />
                <Button
                  icon={<SettingOutlined />}
                  size="large"
                  block
                  type="dashed"
                  style={{ height: 48, borderRadius: 8 }}
                >
                  系统设置
                </Button>
              </Space>
            </ProCard>
          </Col>

          {/* 最近活动 */}
          <Col xs={24} sm={24} md={12} lg={8} xl={8} xxl={8}>
            <ProCard
              title="最近活动"
              headerBordered
              style={{ height: '100%', borderRadius: 12 }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>
                  <Text strong>张三</Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>加入了团队</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>2 小时前</Text>
                </div>
                <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>
                  <Text strong>李四</Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>完成了任务</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>4 小时前</Text>
                </div>
                <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>
                  <Text strong>王五</Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>创建了新项目</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>1 天前</Text>
                </div>
                <div style={{ padding: '12px 0' }}>
                  <Text strong>赵六</Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>更新了文档</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>2 天前</Text>
                </div>
              </Space>
            </ProCard>
          </Col>

          {/* 系统状态 */}
          <Col xs={24} sm={24} md={24} lg={8} xl={8} xxl={8}>
            <ProCard
              title="系统状态"
              headerBordered
              style={{ height: '100%', borderRadius: 12 }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>服务器状态</Text>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: '#059669',
                      marginRight: 8
                    }} />
                    <Text type="success">正常</Text>
                  </div>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>数据库连接</Text>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: '#059669',
                      marginRight: 8
                    }} />
                    <Text type="success">正常</Text>
                  </div>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>存储空间</Text>
                  <Text>78% 已使用</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>在线用户</Text>
                  <Text strong style={{ color: '#2563eb' }}>24 人</Text>
                </div>
                <Divider style={{ margin: '12px 0' }} />
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    最后更新：刚刚
                  </Text>
                </div>
              </Space>
            </ProCard>
          </Col>
        </Row>
      </div>
    </PageContainer>
  );
};

export default Dashboard;
