package com.teammanage.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 订阅套餐实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("subscription_plan")
public class SubscriptionPlan extends BaseEntity {

    /**
     * 套餐名称
     */
    @NotBlank(message = "套餐名称不能为空")
    @Size(max = 50, message = "套餐名称长度不能超过50个字符")
    private String name;

    /**
     * 套餐说明
     */
    private String description;

    /**
     * 数据数量上限
     */
    @NotNull(message = "数据数量上限不能为空")
    @Min(value = 1, message = "数据数量上限必须大于0")
    private Integer maxSize;

    /**
     * 价格(元/月)
     */
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.00", message = "价格不能为负数")
    private BigDecimal price;

    /**
     * 是否启用
     */
    private Boolean isActive;

}
