package com.teammanage.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.ibatis.type.JdbcType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.teammanage.enums.TeamRole;

/**
 * TeamRoleTypeHandler 测试类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TeamRoleTypeHandlerTest {

    private TeamRoleTypeHandler typeHandler;
    private PreparedStatement preparedStatement;
    private ResultSet resultSet;

    @BeforeEach
    public void setUp() {
        typeHandler = new TeamRoleTypeHandler();
        preparedStatement = mock(PreparedStatement.class);
        resultSet = mock(ResultSet.class);
    }

    @Test
    public void testSetNonNullParameter() throws SQLException {
        // 测试设置 TEAM_CREATOR
        typeHandler.setNonNullParameter(preparedStatement, 1, TeamRole.TEAM_CREATOR, JdbcType.INTEGER);
        verify(preparedStatement).setInt(1, 100);

        // 测试设置 TEAM_MEMBER
        typeHandler.setNonNullParameter(preparedStatement, 2, TeamRole.TEAM_MEMBER, JdbcType.INTEGER);
        verify(preparedStatement).setInt(2, 10);
    }

    @Test
    public void testGetNullableResultByColumnName() throws SQLException {
        // 测试获取 TEAM_CREATOR
        when(resultSet.getInt("role")).thenReturn(100);
        when(resultSet.wasNull()).thenReturn(false);
        TeamRole result = typeHandler.getNullableResult(resultSet, "role");
        assertEquals(TeamRole.TEAM_CREATOR, result);

        // 测试获取 TEAM_MEMBER
        when(resultSet.getInt("role")).thenReturn(10);
        when(resultSet.wasNull()).thenReturn(false);
        result = typeHandler.getNullableResult(resultSet, "role");
        assertEquals(TeamRole.TEAM_MEMBER, result);

        // 测试 null 值
        when(resultSet.getInt("role")).thenReturn(0);
        when(resultSet.wasNull()).thenReturn(true);
        result = typeHandler.getNullableResult(resultSet, "role");
        assertNull(result);

        // 测试无效值
        when(resultSet.getInt("role")).thenReturn(999);
        when(resultSet.wasNull()).thenReturn(false);
        result = typeHandler.getNullableResult(resultSet, "role");
        assertNull(result);
    }

    @Test
    public void testGetNullableResultByColumnIndex() throws SQLException {
        // 测试获取 TEAM_CREATOR
        when(resultSet.getInt(1)).thenReturn(100);
        when(resultSet.wasNull()).thenReturn(false);
        TeamRole result = typeHandler.getNullableResult(resultSet, 1);
        assertEquals(TeamRole.TEAM_CREATOR, result);

        // 测试获取 TEAM_MEMBER
        when(resultSet.getInt(1)).thenReturn(10);
        when(resultSet.wasNull()).thenReturn(false);
        result = typeHandler.getNullableResult(resultSet, 1);
        assertEquals(TeamRole.TEAM_MEMBER, result);
    }
}
