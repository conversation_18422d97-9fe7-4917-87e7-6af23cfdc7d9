.popoverContent-EhVEGF9V {
  padding: 16px;
  min-width: 320px;
  max-width: 380px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(37, 99, 235, 0.12);
}
.popoverTitle-gZ0PK1Dm {
  padding: 0 0 12px 0;
  border-bottom: 1px solid rgba(37, 99, 235, 0.08);
  margin-bottom: 16px;
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
}
.infoItem-TRvgqB7k {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 0 0 8px 0;
  position: relative;
  background: rgba(248, 250, 252, 0.5);
  border: 1px solid rgba(37, 99, 235, 0.04);
}
.infoItem-TRvgqB7k:hover {
  background: rgba(37, 99, 235, 0.06);
  border-color: rgba(37, 99, 235, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.08);
}
.infoItem-TRvgqB7k.email-YepxYkkf:hover {
  background: rgba(24, 144, 255, 0.04);
}
.infoItem-TRvgqB7k.phone-EFJPU7Vm:hover {
  background: rgba(82, 196, 26, 0.04);
}
.infoItem-TRvgqB7k.register-NbpyKnYq:hover {
  background: rgba(114, 46, 209, 0.04);
}
.infoItem-TRvgqB7k.lastLogin-_0fqwZFk:hover {
  background: rgba(250, 140, 22, 0.04);
}
.infoItem-TRvgqB7k.team-iiXX2L4w:hover {
  background: rgba(19, 194, 194, 0.04);
}
.iconWrapper-HCDP4HDG {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border: 1px solid rgba(37, 99, 235, 0.12);
  flex-shrink: 0;
  transition: all 0.2s ease;
}
.icon-z1rS9q3h {
  font-size: 14px;
  font-weight: 600;
}
.infoContent-AvzO53H- {
  flex: 1 1;
  min-width: 0;
}
.label-fY2ApLsi {
  display: block;
  font-size: 12px;
  line-height: 1.3;
  margin-bottom: 4px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.value-ClCU1LzT {
  display: block;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  color: #1f2937;
  word-break: break-all;
}
.trigger-vd13A828 {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  padding: 4px 6px;
  margin: -4px -6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.trigger-vd13A828:hover {
  background: rgba(24, 144, 255, 0.06);
  transform: scale(1.05);
}
.questionIcon-GFk04_0Q {
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 6px;
  border-radius: 8px;
  background: rgba(37, 99, 235, 0.04);
  border: 1px solid rgba(37, 99, 235, 0.08);
}
.questionIcon-GFk04_0Q:hover {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.15);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}
.settingIcon-JoqAnTNp {
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 6px;
  border-radius: 8px;
  background: rgba(37, 99, 235, 0.04);
  border: 1px solid rgba(37, 99, 235, 0.08);
}
.settingIcon-JoqAnTNp:hover {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.15);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}
@media (max-width: 768px) {
  .popoverContent-EhVEGF9V {
    min-width: 280px;
    max-width: 320px;
  }
  .infoItem-TRvgqB7k {
    gap: 10px;
    padding: 8px 0;
  }
  .iconWrapper-HCDP4HDG {
    width: 24px;
    height: 24px;
  }
  .icon-z1rS9q3h {
    font-size: 12px;
  }
  .label-fY2ApLsi {
    font-size: 11px;
  }
  .value-ClCU1LzT {
    font-size: 13px;
  }
}
@media (max-width: 576px) {
  .popoverContent-EhVEGF9V {
    min-width: 260px;
    max-width: 300px;
  }
  .popoverTitle-gZ0PK1Dm {
    font-size: 13px;
    padding: 6px 0 10px 0;
    margin-bottom: 10px;
  }
  .infoItem-TRvgqB7k {
    gap: 8px;
    padding: 6px 0;
  }
  .iconWrapper-HCDP4HDG {
    width: 22px;
    height: 22px;
  }
  .icon-z1rS9q3h {
    font-size: 11px;
  }
  .label-fY2ApLsi {
    font-size: 10px;
    margin-bottom: 2px;
  }
  .value-ClCU1LzT {
    font-size: 12px;
  }
}
@keyframes fadeIn-Bxe6iemy {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.popoverContent-EhVEGF9V {
  -webkit-animation: fadeIn 0.2s ease-out;
  animation: fadeIn-Bxe6iemy 0.2s ease-out;
}
.divider-kogfDmru {
  margin: 8px 0;
  border-color: #f0f0f0;
}
.value-ClCU1LzT .ant-typography-copy {
  color: #8c8c8c;
  margin-left: 4px;
  opacity: 0.7;
  transition: all 0.2s ease;
}
.value-ClCU1LzT:hover .ant-typography-copy {
  opacity: 1;
  color: #1890ff;
}
.ant-tooltip {
  z-index: 1070 !important;
}
.ant-tooltip-inner {
  background-color: rgba(0, 0, 0, 0.85) !important;
  color: #fff !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
  min-height: auto !important;
  min-width: auto !important;
  word-wrap: break-word !important;
  word-break: normal !important;
}
.ant-tooltip-arrow {
  z-index: 1071 !important;
}
.ant-tooltip-placement-top .ant-tooltip-arrow, 
.ant-tooltip-placement-topLeft .ant-tooltip-arrow, 
.ant-tooltip-placement-topRight .ant-tooltip-arrow {
  bottom: -6px !important;
}
.ant-tooltip-placement-bottom .ant-tooltip-arrow, 
.ant-tooltip-placement-bottomLeft .ant-tooltip-arrow, 
.ant-tooltip-placement-bottomRight .ant-tooltip-arrow {
  top: -6px !important;
}
.ant-tooltip-placement-left .ant-tooltip-arrow, 
.ant-tooltip-placement-leftTop .ant-tooltip-arrow, 
.ant-tooltip-placement-leftBottom .ant-tooltip-arrow {
  right: -6px !important;
}
.ant-tooltip-placement-right .ant-tooltip-arrow, 
.ant-tooltip-placement-rightTop .ant-tooltip-arrow, 
.ant-tooltip-placement-rightBottom .ant-tooltip-arrow {
  left: -6px !important;
}
.ant-tooltip-hidden {
  display: none !important;
}
.ant-tooltip {
  max-width: 250px !important;
  word-wrap: break-word !important;
}
@media (max-width: 768px) {
  .ant-tooltip {
    max-width: 200px !important;
  }
  .ant-tooltip-inner {
    font-size: 13px !important;
    padding: 6px 10px !important;
  }
}
.ant-tooltip {
  opacity: 1 !important;
}
.ant-tooltip-inner {
  opacity: 1 !important;
}
.ant-tooltip-inner {
  color: #ffffff !important;
  text-shadow: none !important;
}
.ant-tooltip-inner {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif !important;
  font-weight: 400 !important;
}
/*# sourceMappingURL=src_pages_personal-center_index_tsx-async.css.map*/