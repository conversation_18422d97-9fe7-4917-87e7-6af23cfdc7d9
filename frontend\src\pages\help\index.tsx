import {
  BookOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  TeamOutlined,
  UserOutlined,
  SafetyOutlined,
  PhoneOutlined,
  MailOutlined,
  ClockCircleOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { Button, Divider, Space, Typography, Row, Col, Card, Steps } from 'antd';
import React from 'react';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;

const HelpPage: React.FC = () => {
  return (
    <PageContainer
      title="帮助中心"
      subTitle="团队协作管理系统使用指南"
      extra={[
        <Button key="contact" type="primary" icon={<PhoneOutlined />}>
          联系技术支持
        </Button>,
      ]}
    >
      <div style={{ padding: '0 24px' }}>
        {/* 快速导航 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 32 }}>
          <Col xs={12} sm={8} md={6} lg={4} xl={4} xxl={4}>
            <Card
              hoverable
              style={{ textAlign: 'center', borderRadius: 12 }}
              bodyStyle={{ padding: '24px 16px' }}
            >
              <BookOutlined style={{ fontSize: 32, color: '#2563eb', marginBottom: 12 }} />
              <div>
                <Text strong>快速开始</Text>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4} xxl={4}>
            <Card
              hoverable
              style={{ textAlign: 'center', borderRadius: 12 }}
              bodyStyle={{ padding: '24px 16px' }}
            >
              <TeamOutlined style={{ fontSize: 32, color: '#059669', marginBottom: 12 }} />
              <div>
                <Text strong>团队管理</Text>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4} xxl={4}>
            <Card
              hoverable
              style={{ textAlign: 'center', borderRadius: 12 }}
              bodyStyle={{ padding: '24px 16px' }}
            >
              <UserOutlined style={{ fontSize: 32, color: '#d97706', marginBottom: 12 }} />
              <div>
                <Text strong>用户设置</Text>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4} xxl={4}>
            <Card
              hoverable
              style={{ textAlign: 'center', borderRadius: 12 }}
              bodyStyle={{ padding: '24px 16px' }}
            >
              <SettingOutlined style={{ fontSize: 32, color: '#dc2626', marginBottom: 12 }} />
              <div>
                <Text strong>系统设置</Text>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4} xxl={4}>
            <Card
              hoverable
              style={{ textAlign: 'center', borderRadius: 12 }}
              bodyStyle={{ padding: '24px 16px' }}
            >
              <SafetyOutlined style={{ fontSize: 32, color: '#7c3aed', marginBottom: 12 }} />
              <div>
                <Text strong>安全中心</Text>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4} xl={4} xxl={4}>
            <Card
              hoverable
              style={{ textAlign: 'center', borderRadius: 12 }}
              bodyStyle={{ padding: '24px 16px' }}
            >
              <QuestionCircleOutlined style={{ fontSize: 32, color: '#0891b2', marginBottom: 12 }} />
              <div>
                <Text strong>常见问题</Text>
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]}>
          {/* 左侧内容区域 */}
          <Col xs={24} sm={24} md={16} lg={16} xl={16} xxl={16}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* 快速开始 */}
              <ProCard
                title={
                  <Space>
                    <BookOutlined style={{ color: '#2563eb' }} />
                    <span>快速开始</span>
                  </Space>
                }
                headerBordered
                style={{ borderRadius: 12 }}
              >
                <Paragraph style={{ fontSize: 16, marginBottom: 24 }}>
                  欢迎使用团队协作管理系统！本系统帮助您高效管理团队成员、项目和任务。
                </Paragraph>

                <Title level={4} style={{ marginTop: 24 }}>首次使用步骤</Title>
                <Steps
                  direction="vertical"
                  size="small"
                  current={-1}
                  style={{ marginTop: 16 }}
                >
                  <Step
                    title="注册账号并登录系统"
                    description="使用邮箱注册账号，验证后即可登录使用"
                    icon={<UserOutlined />}
                  />
                  <Step
                    title="创建或加入团队"
                    description="创建新团队或通过邀请链接加入现有团队"
                    icon={<TeamOutlined />}
                  />
                  <Step
                    title="邀请团队成员"
                    description="通过邮箱邀请同事加入您的团队"
                    icon={<MailOutlined />}
                  />
                  <Step
                    title="开始协作管理"
                    description="创建项目、分配任务，开始高效协作"
                    icon={<RightOutlined />}
                  />
                </Steps>
              </ProCard>

              {/* 功能介绍 */}
              <ProCard
                title={
                  <Space>
                    <TeamOutlined style={{ color: '#059669' }} />
                    <span>主要功能</span>
                  </Space>
                }
                headerBordered
                style={{ borderRadius: 12 }}
              >
                <Row gutter={[16, 16]}>
                  <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
                    <div style={{ padding: '16px 0' }}>
                      <Title level={5} style={{ color: '#2563eb', marginBottom: 8 }}>
                        <TeamOutlined style={{ marginRight: 8 }} />
                        团队管理
                      </Title>
                      <Paragraph style={{ marginBottom: 0, color: '#6b7280' }}>
                        创建团队、邀请成员、设置权限，构建高效协作团队
                      </Paragraph>
                    </div>
                  </Col>
                  <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
                    <div style={{ padding: '16px 0' }}>
                      <Title level={5} style={{ color: '#2563eb', marginBottom: 8 }}>
                        <BookOutlined style={{ marginRight: 8 }} />
                        项目协作
                      </Title>
                      <Paragraph style={{ marginBottom: 0, color: '#6b7280' }}>
                        创建项目、分配任务、跟踪进度，确保项目顺利交付
                      </Paragraph>
                    </div>
                  </Col>
                  <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
                    <div style={{ padding: '16px 0' }}>
                      <Title level={5} style={{ color: '#2563eb', marginBottom: 8 }}>
                        <SafetyOutlined style={{ marginRight: 8 }} />
                        文档管理
                      </Title>
                      <Paragraph style={{ marginBottom: 0, color: '#6b7280' }}>
                        上传文档、版本控制、共享协作，知识管理更轻松
                      </Paragraph>
                    </div>
                  </Col>
                  <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
                    <div style={{ padding: '16px 0' }}>
                      <Title level={5} style={{ color: '#2563eb', marginBottom: 8 }}>
                        <MailOutlined style={{ marginRight: 8 }} />
                        消息通知
                      </Title>
                      <Paragraph style={{ marginBottom: 0, color: '#6b7280' }}>
                        实时消息、邮件提醒、状态更新，保持团队同步
                      </Paragraph>
                    </div>
                  </Col>
                </Row>
              </ProCard>
            </Space>
          </Col>

          {/* 右侧边栏 */}
          <Col xs={24} sm={24} md={8} lg={8} xl={8} xxl={8}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* 联系我们 */}
              <ProCard
                title={
                  <Space>
                    <PhoneOutlined style={{ color: '#dc2626' }} />
                    <span>联系我们</span>
                  </Space>
                }
                headerBordered
                style={{ borderRadius: 12 }}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>
                    <MailOutlined style={{ color: '#2563eb', marginRight: 12, fontSize: 16 }} />
                    <div>
                      <Text strong>技术支持邮箱</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}><EMAIL></Text>
                    </div>
                  </div>
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>
                    <MailOutlined style={{ color: '#059669', marginRight: 12, fontSize: 16 }} />
                    <div>
                      <Text strong>用户反馈</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}><EMAIL></Text>
                    </div>
                  </div>
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>
                    <ClockCircleOutlined style={{ color: '#d97706', marginRight: 12, fontSize: 16 }} />
                    <div>
                      <Text strong>工作时间</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>周一至周五 9:00-18:00</Text>
                    </div>
                  </div>
                </Space>
              </ProCard>

              {/* 常见问题 */}
              <ProCard
                title={
                  <Space>
                    <QuestionCircleOutlined style={{ color: '#0891b2' }} />
                    <span>常见问题</span>
                  </Space>
                }
                headerBordered
                style={{ borderRadius: 12 }}
              >
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div>
                    <Title level={5} style={{ color: '#2563eb', marginBottom: 8 }}>
                      Q: 如何切换团队？
                    </Title>
                    <Paragraph style={{ color: '#6b7280', marginBottom: 0 }}>
                      A: 在顶部导航栏的团队名称处点击，可以选择切换到其他团队。
                    </Paragraph>
                  </div>
                  <Divider />
                  <div>
                    <Title level={5} style={{ color: '#2563eb', marginBottom: 8 }}>
                      Q: 忘记密码怎么办？
                    </Title>
                    <Paragraph style={{ color: '#6b7280', marginBottom: 0 }}>
                      A: 在登录页面使用邮箱验证码登录，系统会自动处理身份验证。
                    </Paragraph>
                  </div>
                  <Divider />
                  <div>
                    <Title level={5} style={{ color: '#2563eb', marginBottom: 8 }}>
                      Q: 如何邀请新成员？
                    </Title>
                    <Paragraph style={{ color: '#6b7280', marginBottom: 0 }}>
                      A: 团队管理员可以在团队管理页面通过邮箱邀请新成员。
                    </Paragraph>
                  </div>
                  <Divider />
                  <div>
                    <Title level={5} style={{ color: '#2563eb', marginBottom: 8 }}>
                      Q: 如何管理团队权限？
                    </Title>
                    <Paragraph style={{ color: '#6b7280', marginBottom: 0 }}>
                      A: 团队管理员可以在团队设置中为不同成员分配不同的角色和权限。
                    </Paragraph>
                  </div>
                </Space>
              </ProCard>
            </Space>
          </Col>
        </Row>
      </div>
    </PageContainer>
  );
};

export default HelpPage;
