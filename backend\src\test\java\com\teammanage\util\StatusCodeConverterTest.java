package com.teammanage.util;

import com.teammanage.entity.AccountSubscription.SubscriptionStatus;
import com.teammanage.entity.TeamInvitation.InvitationStatus;
import com.teammanage.enums.TeamRole;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 状态码转换工具测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class StatusCodeConverterTest {

    @Test
    public void testTeamRoleConverter() {
        // 测试枚举转状态码
        assertEquals(Integer.valueOf(100), StatusCodeConverter.TeamRoleConverter.toCode(TeamRole.TEAM_CREATOR));
        assertEquals(Integer.valueOf(10), StatusCodeConverter.TeamRoleConverter.toCode(TeamRole.TEAM_MEMBER));
        assertNull(StatusCodeConverter.TeamRoleConverter.toCode(null));

        // 测试状态码转枚举
        assertEquals(TeamRole.TEAM_CREATOR, StatusCodeConverter.TeamRoleConverter.fromCode(100));
        assertEquals(TeamRole.TEAM_MEMBER, StatusCodeConverter.TeamRoleConverter.fromCode(10));
        assertNull(StatusCodeConverter.TeamRoleConverter.fromCode(999));
        assertNull(StatusCodeConverter.TeamRoleConverter.fromCode(null));

        // 测试有效性检查
        assertTrue(StatusCodeConverter.TeamRoleConverter.isValidCode(100));
        assertTrue(StatusCodeConverter.TeamRoleConverter.isValidCode(10));
        assertFalse(StatusCodeConverter.TeamRoleConverter.isValidCode(999));
        assertFalse(StatusCodeConverter.TeamRoleConverter.isValidCode(null));

        // 测试默认邀请角色
        assertEquals(Integer.valueOf(10), StatusCodeConverter.TeamRoleConverter.getDefaultInvitationRoleCode());
    }

    @Test
    public void testInvitationStatusConverter() {
        // 测试枚举转状态码
        assertEquals(Integer.valueOf(1), StatusCodeConverter.InvitationStatusConverter.toCode(InvitationStatus.PENDING));
        assertEquals(Integer.valueOf(2), StatusCodeConverter.InvitationStatusConverter.toCode(InvitationStatus.ACCEPTED));
        assertEquals(Integer.valueOf(3), StatusCodeConverter.InvitationStatusConverter.toCode(InvitationStatus.REJECTED));
        assertEquals(Integer.valueOf(4), StatusCodeConverter.InvitationStatusConverter.toCode(InvitationStatus.EXPIRED));
        assertEquals(Integer.valueOf(5), StatusCodeConverter.InvitationStatusConverter.toCode(InvitationStatus.CANCELLED));
        assertNull(StatusCodeConverter.InvitationStatusConverter.toCode(null));

        // 测试状态码转枚举
        assertEquals(InvitationStatus.PENDING, StatusCodeConverter.InvitationStatusConverter.fromCode(1));
        assertEquals(InvitationStatus.ACCEPTED, StatusCodeConverter.InvitationStatusConverter.fromCode(2));
        assertEquals(InvitationStatus.REJECTED, StatusCodeConverter.InvitationStatusConverter.fromCode(3));
        assertEquals(InvitationStatus.EXPIRED, StatusCodeConverter.InvitationStatusConverter.fromCode(4));
        assertEquals(InvitationStatus.CANCELLED, StatusCodeConverter.InvitationStatusConverter.fromCode(5));
        assertNull(StatusCodeConverter.InvitationStatusConverter.fromCode(999));
        assertNull(StatusCodeConverter.InvitationStatusConverter.fromCode(null));

        // 测试有效性检查
        assertTrue(StatusCodeConverter.InvitationStatusConverter.isValidCode(1));
        assertTrue(StatusCodeConverter.InvitationStatusConverter.isValidCode(5));
        assertFalse(StatusCodeConverter.InvitationStatusConverter.isValidCode(999));
        assertFalse(StatusCodeConverter.InvitationStatusConverter.isValidCode(null));

        // 测试状态码常量获取
        assertEquals(Integer.valueOf(1), StatusCodeConverter.InvitationStatusConverter.getPendingCode());
        assertEquals(Integer.valueOf(2), StatusCodeConverter.InvitationStatusConverter.getAcceptedCode());
        assertEquals(Integer.valueOf(3), StatusCodeConverter.InvitationStatusConverter.getRejectedCode());
        assertEquals(Integer.valueOf(5), StatusCodeConverter.InvitationStatusConverter.getCancelledCode());

        // 测试业务逻辑方法
        assertTrue(StatusCodeConverter.InvitationStatusConverter.canBeResponded(1)); // PENDING
        assertFalse(StatusCodeConverter.InvitationStatusConverter.canBeResponded(2)); // ACCEPTED
        assertFalse(StatusCodeConverter.InvitationStatusConverter.canBeResponded(null));

        assertTrue(StatusCodeConverter.InvitationStatusConverter.canBeCancelled(1)); // PENDING
        assertFalse(StatusCodeConverter.InvitationStatusConverter.canBeCancelled(2)); // ACCEPTED
        assertFalse(StatusCodeConverter.InvitationStatusConverter.canBeCancelled(null));
    }

    @Test
    public void testSubscriptionStatusConverter() {
        // 测试枚举转状态码
        assertEquals(Integer.valueOf(1), StatusCodeConverter.SubscriptionStatusConverter.toCode(SubscriptionStatus.ACTIVE));
        assertEquals(Integer.valueOf(2), StatusCodeConverter.SubscriptionStatusConverter.toCode(SubscriptionStatus.EXPIRED));
        assertEquals(Integer.valueOf(3), StatusCodeConverter.SubscriptionStatusConverter.toCode(SubscriptionStatus.CANCELED));
        assertNull(StatusCodeConverter.SubscriptionStatusConverter.toCode(null));

        // 测试状态码转枚举
        assertEquals(SubscriptionStatus.ACTIVE, StatusCodeConverter.SubscriptionStatusConverter.fromCode(1));
        assertEquals(SubscriptionStatus.EXPIRED, StatusCodeConverter.SubscriptionStatusConverter.fromCode(2));
        assertEquals(SubscriptionStatus.CANCELED, StatusCodeConverter.SubscriptionStatusConverter.fromCode(3));
        assertNull(StatusCodeConverter.SubscriptionStatusConverter.fromCode(999));
        assertNull(StatusCodeConverter.SubscriptionStatusConverter.fromCode(null));

        // 测试有效性检查
        assertTrue(StatusCodeConverter.SubscriptionStatusConverter.isValidCode(1));
        assertTrue(StatusCodeConverter.SubscriptionStatusConverter.isValidCode(3));
        assertFalse(StatusCodeConverter.SubscriptionStatusConverter.isValidCode(999));
        assertFalse(StatusCodeConverter.SubscriptionStatusConverter.isValidCode(null));

        // 测试状态码常量获取
        assertEquals(Integer.valueOf(1), StatusCodeConverter.SubscriptionStatusConverter.getActiveCode());
        assertEquals(Integer.valueOf(3), StatusCodeConverter.SubscriptionStatusConverter.getCanceledCode());

        // 测试业务逻辑方法
        assertTrue(StatusCodeConverter.SubscriptionStatusConverter.isActive(1)); // ACTIVE
        assertFalse(StatusCodeConverter.SubscriptionStatusConverter.isActive(2)); // EXPIRED
        assertFalse(StatusCodeConverter.SubscriptionStatusConverter.isActive(null));
    }

    @Test
    public void testConverterIntegration() {
        // 测试完整的转换流程
        TeamRole originalRole = TeamRole.TEAM_CREATOR;
        
        // 枚举 -> 状态码 -> 枚举
        Integer code = StatusCodeConverter.TeamRoleConverter.toCode(originalRole);
        TeamRole convertedRole = StatusCodeConverter.TeamRoleConverter.fromCode(code);
        
        assertEquals(originalRole, convertedRole);
        assertEquals(Integer.valueOf(100), code);

        // 测试邀请状态转换流程
        InvitationStatus originalStatus = InvitationStatus.PENDING;
        
        Integer statusCode = StatusCodeConverter.InvitationStatusConverter.toCode(originalStatus);
        InvitationStatus convertedStatus = StatusCodeConverter.InvitationStatusConverter.fromCode(statusCode);
        
        assertEquals(originalStatus, convertedStatus);
        assertEquals(Integer.valueOf(1), statusCode);

        // 测试订阅状态转换流程
        SubscriptionStatus originalSubStatus = SubscriptionStatus.ACTIVE;
        
        Integer subStatusCode = StatusCodeConverter.SubscriptionStatusConverter.toCode(originalSubStatus);
        SubscriptionStatus convertedSubStatus = StatusCodeConverter.SubscriptionStatusConverter.fromCode(subStatusCode);
        
        assertEquals(originalSubStatus, convertedSubStatus);
        assertEquals(Integer.valueOf(1), subStatusCode);
    }
}
