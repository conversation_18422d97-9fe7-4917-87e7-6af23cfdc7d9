/**
 * 登录页面
 * 实现双阶段认证的第一阶段：账号登录
 */

import { MailOutlined, SafetyOutlined, LockOutlined, UserOutlined } from '@ant-design/icons';
import { Helmet, history, useModel } from '@umijs/max';
import {
  Button,
  Form,
  Input,
  message,
  Space,
  Typography,
  Row,
  Col,
  Card,
  Divider,
} from 'antd';
import { ProCard } from '@ant-design/pro-components';
import { createStyles } from 'antd-style';
import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Footer } from '@/components';
import { AuthService } from '@/services';
import type { LoginRequest, SendVerificationCodeRequest } from '@/types/api';
import Settings from '../../../../config/defaultSettings';

const { Title, Text } = Typography;

// 登录表单组件（移到外部避免重新创建）
const LoginFormComponent: React.FC<{
  form: any;
  handleLogin: (values: LoginRequest) => void;
  handleSendCode: () => void;
  sendingCode: boolean;
  countdown: number;
  loading: boolean;
}> = React.memo(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading }) => {
  // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染
  const sendCodeButton = useMemo(() => (
    <Button
      type="link"
      size="small"
      disabled={countdown > 0 || sendingCode}
      loading={sendingCode}
      onClick={handleSendCode}
      style={{ padding: 0, height: 'auto' }}
    >
      {countdown > 0 ? `${countdown}s后重发` : '发送验证码'}
    </Button>
  ), [countdown, sendingCode, handleSendCode]);

  // 使用 useMemo 稳定邮箱输入框，避免重新渲染
  const emailField = useMemo(() => (
    <Form.Item
      key="email-field"
      name="email"
      rules={[
        { required: true, message: '请输入邮箱！' },
        { type: 'email', message: '请输入有效的邮箱地址！' },
      ]}
    >
      <Input
        key="email-input"
        prefix={<MailOutlined />}
        placeholder="邮箱"
        autoComplete="email"
      />
    </Form.Item>
  ), []);

  // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染
  const codeField = useMemo(() => (
    <Form.Item
      key="code-field"
      name="code"
      rules={[
        { required: true, message: '请输入验证码！' },
        { len: 6, message: '验证码为6位数字！' },
        { pattern: /^\d{6}$/, message: '验证码只能包含数字！' },
      ]}
    >
      <Input
        key="code-input"
        prefix={<SafetyOutlined />}
        placeholder="6位验证码"
        maxLength={6}
        suffix={sendCodeButton}
      />
    </Form.Item>
  ), [sendCodeButton]);

  return (
    <Form
      form={form}
      name="login"
      size="large"
      onFinish={handleLogin}
      autoComplete="off"
    >
      {emailField}
      {codeField}

      {/* 提示信息 */}
      <div style={{ marginBottom: 16, textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          新用户将自动完成注册并登录
        </Text>
      </div>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading} block>
          登录 / 注册
        </Button>
      </Form.Item>
    </Form>
  );
});

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        zIndex: 0,
      },
    },
    content: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '32px 16px',
      position: 'relative',
      zIndex: 1,
      '@media (max-width: 768px)': {
        padding: '24px 12px',
      },
      '@media (max-width: 480px)': {
        padding: '16px 8px',
      },
    },
    header: {
      marginBottom: 40,
      textAlign: 'center',
      '@media (max-width: 768px)': {
        marginBottom: 32,
      },
      '@media (max-width: 480px)': {
        marginBottom: 24,
      },
    },
    logo: {
      marginBottom: 16,
      '@media (max-width: 480px)': {
        marginBottom: 12,
        '& img': {
          height: '40px !important',
        },
      },
    },
    title: {
      marginBottom: 0,
      '& h2': {
        color: 'white',
        fontSize: '32px',
        fontWeight: 600,
        marginBottom: '8px',
        '@media (max-width: 768px)': {
          fontSize: '28px',
        },
        '@media (max-width: 480px)': {
          fontSize: '24px',
        },
      },
      '& .ant-typography': {
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '16px',
        '@media (max-width: 768px)': {
          fontSize: '15px',
        },
        '@media (max-width: 480px)': {
          fontSize: '14px',
        },
      },
    },
    loginCard: {
      width: '100%',
      maxWidth: 420,
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(20px)',
      borderRadius: '16px',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
      padding: '40px 32px',
      '@media (max-width: 768px)': {
        maxWidth: '380px',
        padding: '32px 24px',
        borderRadius: '12px',
      },
      '@media (max-width: 480px)': {
        maxWidth: '100%',
        margin: '0 8px',
        padding: '24px 20px',
        borderRadius: '8px',
      },
    },
    footer: {
      marginTop: 40,
      textAlign: 'center',
      '@media (max-width: 768px)': {
        marginTop: 32,
      },
      '@media (max-width: 480px)': {
        marginTop: 24,
      },
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: '42px',
      position: 'fixed',
      right: 16,
      top: 16,
      borderRadius: token.borderRadius,
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      color: 'white',
      zIndex: 10,
      '@media (max-width: 480px)': {
        width: 36,
        height: 36,
        lineHeight: '36px',
        right: 12,
        top: 12,
      },
      ':hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
      },
    },
    formTitle: {
      textAlign: 'center',
      marginBottom: 32,
      '& h3': {
        color: '#2563eb',
        fontSize: '24px',
        fontWeight: 600,
        marginBottom: '8px',
        '@media (max-width: 480px)': {
          fontSize: '20px',
        },
      },
      '& .ant-typography': {
        color: '#6b7280',
        fontSize: '14px',
      },
    },
  };
});

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [form] = Form.useForm(); // 将表单实例提升到父组件
  const { styles } = useStyles();
  const { setInitialState } = useModel('@@initialState');

  // 使用 Form 内置的邮箱验证

  // 组件挂载时清除倒计时状态，避免页面刷新后无法输入
  useEffect(() => {
    setCountdown(0);
  }, []);

  // 倒计时效果
  React.useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  // 发送验证码
  const handleSendCode = useCallback(async (type: 'login' | 'register' = 'login') => {
    let email: string;

    try {
      // 验证邮箱字段
      await form.validateFields(['email']);

      // 从表单获取邮箱值
      email = form.getFieldValue('email');

      if (!email) {
        return;
      }
    } catch (error) {
      // 表单验证失败，由表单验证规则处理错误显示
      return;
    }

    setSendingCode(true);
    try {
      const request: SendVerificationCodeRequest = { email, type };
      const response = await AuthService.sendVerificationCode(request);

      if (response.success) {
        setCountdown(60); // 60秒倒计时
      } else {
        if (response.nextSendTime) {
          setCountdown(response.nextSendTime);
        }
      }
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    } finally {
      setSendingCode(false);
    }
  }, [form]);

  // 处理登录/注册
  const handleLogin = useCallback(async (values: LoginRequest) => {
    setLoading(true);
    try {
      const response = await AuthService.login(values);

      // 登录成功后停止倒计时
      setCountdown(0);

      // 登录成功后，刷新 initialState
      await setInitialState((prevState) => ({
        ...prevState,
        currentUser: response.user,
        currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,
      }));

      // 根据团队数量进行不同的跳转处理
      if (response.teams.length === 0) {
        // 没有团队，跳转到个人中心页面
        history.push('/personal-center');
      } else {
        // 有团队（无论一个还是多个），都跳转到个人中心整合页面
        history.push('/personal-center', { teams: response.teams });
      }
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    } finally {
      setLoading(false);
    }
  }, [setInitialState]);

  // 注册功能已移除，统一使用验证码登录/注册流程

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          登录 / 注册
          {Settings.title && ` - ${Settings.title}`}
        </title>
      </Helmet>
      <div className={styles.content}>
        <div className={styles.header}>
          <Space direction="vertical" align="center" size="large">
            <div className={styles.logo}>
              <img src="/logo.svg" alt="TeamAuth" height={48} />
            </div>
            <div className={styles.title}>
              <Title level={2}>团队管理系统</Title>
              <Text type="secondary">现代化的团队协作与管理平台</Text>
            </div>
          </Space>
        </div>

        <div className={styles.loginCard}>
          <div className={styles.formTitle}>
            <Title level={3}>欢迎回来</Title>
            <Text type="secondary">请使用邮箱验证码登录您的账户</Text>
          </div>

          <LoginFormComponent
            form={form}
            handleLogin={handleLogin}
            handleSendCode={() => handleSendCode('login')}
            sendingCode={sendingCode}
            countdown={countdown}
            loading={loading}
          />

          <Divider style={{ margin: '24px 0', color: '#9ca3af' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              安全登录
            </Text>
          </Divider>

          <div style={{ textAlign: 'center' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              登录即表示您同意我们的
              <a href="#" style={{ color: '#2563eb', marginLeft: 4, marginRight: 4 }}>
                服务条款
              </a>
              和
              <a href="#" style={{ color: '#2563eb', marginLeft: 4 }}>
                隐私政策
              </a>
            </Text>
          </div>
        </div>

        <div className={styles.footer}>
          <Footer />
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
