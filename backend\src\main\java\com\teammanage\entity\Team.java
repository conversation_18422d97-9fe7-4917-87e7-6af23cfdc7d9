package com.teammanage.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 团队实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("team")
public class Team extends BaseEntity {

    /**
     * 团队名称
     */
    @NotBlank(message = "团队名称不能为空")
    @Size(max = 100, message = "团队名称长度不能超过100个字符")
    private String name;

    /**
     * 团队描述
     */
    private String description;

    /**
     * 创建人ID
     */
    @NotNull(message = "创建人ID不能为空")
    private Long createdBy;

    /**
     * 删除标记
     */
    @TableLogic
    private Boolean isDeleted;

}
