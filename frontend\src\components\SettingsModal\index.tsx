import {
  DeleteOutlined,
  EditOutlined,
  LogoutOutlined,
  MoreOutlined,
  SaveOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Avatar,
  Button,
  Dropdown,
  Form,
  Input,
  Modal,
  Popconfirm,
  Tabs,
  Typography,
  message,
} from 'antd';
import { ModalForm, ProList, ProCard } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { AuthService, TeamService, UserService } from '@/services';
import type { TeamDetailResponse, UpdateUserProfileRequest } from '@/types/api';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface SettingsModalProps {
  open: boolean;
  onClose: () => void;
}

/**
 * 设置模态框组件
 * 
 * 提供个人资料设置和团队设置的统一界面，包括：
 * - 个人资料编辑（姓名、电话等）
 * - 团队管理（编辑、删除、离开团队）
 * - 响应式设计和友好的用户体验
 */
const SettingsModal: React.FC<SettingsModalProps> = ({ open, onClose }) => {
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const { initialState, setInitialState } = useModel('@@initialState');
  
  // 个人资料相关状态
  const [profileLoading, setProfileLoading] = useState(false);
  
  // 团队相关状态
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [teamsLoading, setTeamsLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingTeam, setEditingTeam] = useState<TeamDetailResponse | null>(null);
  const [editLoading, setEditLoading] = useState(false);
  const [operationLoading, setOperationLoading] = useState<Record<number, boolean>>({});

  /**
   * 获取用户头像显示
   */
  const getUserAvatar = () => {
    const user = initialState?.currentUser;
    if (!user) return 'U';
    
    return user.name ? user.name.charAt(0).toUpperCase() : 
           user.email ? user.email.charAt(0).toUpperCase() : 'U';
  };

  /**
   * 初始化表单数据
   */
  useEffect(() => {
    if (initialState?.currentUser && open) {
      form.setFieldsValue({
        name: initialState.currentUser.name,
        email: initialState.currentUser.email,
        telephone: initialState.currentUser.telephone || '',
      });
    }
  }, [initialState?.currentUser, form, open]);

  /**
   * 获取团队列表
   */
  const fetchTeams = async () => {
    if (!initialState?.currentUser) return;
    
    setTeamsLoading(true);
    try {
      const teamList = await TeamService.getUserTeams();
      setTeams(teamList);
    } catch (error) {
      console.error('获取团队列表失败:', error);
    } finally {
      setTeamsLoading(false);
    }
  };

  /**
   * 当模态框打开时获取团队列表
   */
  useEffect(() => {
    if (open) {
      fetchTeams();
    }
  }, [open]);

  /**
   * 保存个人资料
   */
  const handleSaveProfile = async (values: UpdateUserProfileRequest) => {
    setProfileLoading(true);
    try {
      const updatedUser = await UserService.updateProfile(values);
      
      // 更新全局状态
      await setInitialState((prevState) => ({
        ...prevState,
        currentUser: updatedUser,
      }));

      message.success('个人资料更新成功');
    } catch (error) {
      console.error('更新个人资料失败:', error);
    } finally {
      setProfileLoading(false);
    }
  };

  /**
   * 编辑团队
   */
  const handleEditTeam = (team: TeamDetailResponse) => {
    setEditingTeam(team);
    editForm.setFieldsValue({
      name: team.name,
      description: team.description,
    });
    setEditModalVisible(true);
  };

  /**
   * 保存团队编辑
   */
  const handleSaveTeamEdit = async (values: any) => {
    if (!editingTeam) return;

    setEditLoading(true);
    try {
      // 先切换到目标团队
      await AuthService.selectTeam({ teamId: editingTeam.id });

      // 更新团队信息
      await TeamService.updateCurrentTeam({
        name: values.name,
        description: values.description,
      });

      // 重新获取团队列表
      await fetchTeams();

      // 关闭模态框
      setEditModalVisible(false);
      setEditingTeam(null);
      editForm.resetFields();

      message.success('团队信息更新成功！');
    } catch (error) {
      console.error('更新团队失败:', error);
      message.error('更新团队失败');
    } finally {
      setEditLoading(false);
    }
  };

  /**
   * 删除团队
   */
  const handleDeleteTeam = async (team: TeamDetailResponse) => {
    setOperationLoading(prev => ({ ...prev, [team.id]: true }));
    try {
      // 先切换到目标团队
      await AuthService.selectTeam({ teamId: team.id });
      
      // 删除团队
      await TeamService.deleteCurrentTeam();
      
      // 重新获取团队列表
      await fetchTeams();
      
      message.success('团队删除成功');
    } catch (error) {
      console.error('删除团队失败:', error);
      message.error('删除团队失败');
    } finally {
      setOperationLoading(prev => ({ ...prev, [team.id]: false }));
    }
  };

  /**
   * 离开团队
   */
  const handleLeaveTeam = async (team: TeamDetailResponse) => {
    setOperationLoading(prev => ({ ...prev, [team.id]: true }));
    try {
      // 先切换到目标团队
      await AuthService.selectTeam({ teamId: team.id });

      // 离开团队
      await TeamService.leaveTeam();

      // 重新获取团队列表
      await fetchTeams();

      // 更新全局状态，清除当前团队
      if (setInitialState) {
        await setInitialState((prevState) => ({
          ...prevState,
          currentTeam: undefined,
        }));
      }

      message.success('已成功离开团队');
    } catch (error) {
      console.error('离开团队失败:', error);
      message.error('离开团队失败');
    } finally {
      setOperationLoading(prev => ({ ...prev, [team.id]: false }));
    }
  };

  /**
   * 团队设置面板
   */
  const renderTeamSettings = () => (
    <ProCard
      style={{
        borderRadius: 12,
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
        border: '1px solid #f0f0f0',
      }}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <TeamOutlined
            style={{
              fontSize: '20px',
              color: '#1890ff',
              padding: '6px',
              backgroundColor: '#e6f7ff',
              borderRadius: '6px',
            }}
          />
          <div>
            <Title level={5} style={{ margin: 0 }}>
              团队管理
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              管理您的团队设置和成员权限
            </Text>
          </div>
        </div>
      }
    >
      <ProList
        loading={teamsLoading}
        dataSource={teams}
        renderItem={(team) => {
          const getTeamActions = (team: TeamDetailResponse) => {
            if (team.isCreator) {
              return [
                {
                  key: 'edit',
                  label: '编辑团队名称',
                  icon: <EditOutlined />,
                  onClick: () => handleEditTeam(team),
                },
                {
                  key: 'delete',
                  label: (
                    <Popconfirm
                      title="删除团队"
                      description={`确定要删除团队"${team.name}"吗？此操作不可恢复！`}
                      onConfirm={() => handleDeleteTeam(team)}
                      okText="确定删除"
                      cancelText="取消"
                      okButtonProps={{ danger: true }}
                    >
                      <span style={{ color: '#ff4d4f' }}>删除团队</span>
                    </Popconfirm>
                  ),
                  icon: <DeleteOutlined />,
                  danger: true,
                },
              ];
            } else {
              return [
                {
                  key: 'leave',
                  label: (
                    <Popconfirm
                      title="退出团队"
                      description={`确定要退出团队"${team.name}"吗？`}
                      onConfirm={() => handleLeaveTeam(team)}
                      okText="确定退出"
                      cancelText="取消"
                      okButtonProps={{ danger: true }}
                    >
                      <span style={{ color: '#ff4d4f' }}>退出团队</span>
                    </Popconfirm>
                  ),
                  icon: <LogoutOutlined />,
                  danger: true,
                },
              ];
            }
          };

          return (
            <div
              style={{
                padding: '16px 0',
                borderBottom: '1px solid #f0f0f0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                <Avatar
                  size={40}
                  style={{
                    backgroundColor: team.isCreator ? '#52c41a' : '#1890ff',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    marginRight: 16,
                  }}
                  icon={<TeamOutlined />}
                >
                  {team.name.charAt(0).toUpperCase()}
                </Avatar>
                <div style={{ flex: 1 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <span style={{ fontWeight: 600 }}>{team.name}</span>
                    {team.isCreator && (
                      <span
                        style={{
                          fontSize: '12px',
                          color: '#52c41a',
                          backgroundColor: '#f6ffed',
                          padding: '2px 6px',
                          borderRadius: '4px',
                          border: '1px solid #b7eb8f',
                        }}
                      >
                        创建者
                      </span>
                    )}
                  </div>
                  <div>
                    <Text type="secondary" style={{ fontSize: '13px' }}>
                      {team.description || '暂无描述'}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      成员数量: {team.memberCount} | 创建时间: {new Date(team.createdAt).toLocaleDateString()}
                    </Text>
                  </div>
                </div>
              </div>
              <Dropdown
                menu={{
                  items: getTeamActions(team),
                }}
                trigger={['click']}
                placement="bottomRight"
              >
                <Button
                  type="text"
                  icon={<MoreOutlined />}
                  loading={operationLoading[team.id]}
                  style={{ fontSize: '16px' }}
                />
              </Dropdown>
            </div>
          );
        }}
        locale={{
          emptyText: (
            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
              <TeamOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
              <div style={{ color: '#999', fontSize: 14 }}>
                暂无团队，请先加入或创建团队
              </div>
            </div>
          ),
        }}
      />
    </ProCard>
  );

  /**
   * 个人资料设置面板
   */
  const renderProfileSettings = () => (
    <ProCard
      style={{
        borderRadius: 12,
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
        border: '1px solid #f0f0f0',
      }}
      title={
        <Flex align="center" gap={12}>
          <Avatar
            size={32}
            style={{
              backgroundColor: '#1890ff',
              fontSize: '14px',
              fontWeight: 'bold',
            }}
            icon={<UserOutlined />}
          >
            {getUserAvatar()}
          </Avatar>
          <Flex vertical>
            <Title level={5} style={{ margin: 0 }}>
              个人资料
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              管理您的个人信息和联系方式
            </Text>
          </Flex>
        </Flex>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSaveProfile}
        autoComplete="off"
      >
        <Form.Item
          label="用户名"
          name="name"
          rules={[
            { required: true, message: '请输入用户名' },
            { max: 100, message: '用户名不能超过100个字符' },
            { min: 2, message: '用户名至少需要2个字符' },
          ]}
        >
          <Input
            prefix={<UserOutlined style={{ color: '#1890ff' }} />}
            placeholder="请输入用户名"
            size="large"
          />
        </Form.Item>

        <Form.Item label="邮箱地址" name="email">
          <Input
            prefix={<UserOutlined style={{ color: '#1890ff' }} />}
            disabled
            placeholder="邮箱地址不可修改"
            size="large"
            style={{ backgroundColor: '#f5f5f5' }}
          />
        </Form.Item>

        <Form.Item
          label="联系电话"
          name="telephone"
          rules={[
            { max: 20, message: '联系电话不能超过20个字符' },
            {
              pattern: /^[0-9+\-\s()]*$/,
              message: '请输入有效的电话号码',
            },
          ]}
        >
          <Input
            prefix={<UserOutlined style={{ color: '#1890ff' }} />}
            placeholder="请输入联系电话（可选）"
            size="large"
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={profileLoading}
            icon={<SaveOutlined />}
            size="large"
            style={{
              borderRadius: 8,
              background: 'linear-gradient(135deg, #1890ff, #722ed1)',
              border: 'none',
              boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
              minWidth: 120,
            }}
          >
            保存设置
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );

  return (
    <>
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <UserOutlined style={{ color: '#1890ff' }} />
            <span>设置</span>
          </div>
        }
        open={open}
        onCancel={onClose}
        footer={null}
        width={800}
        style={{ top: 20 }}
        styles={{
          body: { padding: '24px 0' },
        }}
      >
        <Tabs defaultActiveKey="profile" size="large">
          <TabPane
            tab={
              <span>
                <UserOutlined />
                个人资料
              </span>
            }
            key="profile"
          >
            {renderProfileSettings()}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <TeamOutlined />
                团队设置
              </span>
            }
            key="teams"
          >
            {renderTeamSettings()}
          </TabPane>
        </Tabs>
      </Modal>

      {/* 编辑团队模态框 */}
      <ModalForm
        title="编辑团队信息"
        open={editModalVisible}
        onOpenChange={(visible) => {
          if (!visible) {
            setEditModalVisible(false);
            setEditingTeam(null);
            editForm.resetFields();
          }
        }}
        form={editForm}
        layout="vertical"
        onFinish={handleSaveTeamEdit}
        width={500}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          submitButtonProps: {
            loading: editLoading,
            icon: <SaveOutlined />,
          },
          onReset: () => {
            setEditModalVisible(false);
            setEditingTeam(null);
            editForm.resetFields();
          },
        }}
      >
        <Form.Item
          label="团队名称"
          name="name"
          rules={[
            { required: true, message: '请输入团队名称' },
            { max: 50, message: '团队名称不能超过50个字符' },
          ]}
        >
          <Input placeholder="请输入团队名称" />
        </Form.Item>

        <Form.Item
          label="团队描述"
          name="description"
          rules={[{ max: 200, message: '团队描述不能超过200个字符' }]}
        >
          <Input.TextArea
            placeholder="请输入团队描述（可选）"
            rows={3}
          />
        </Form.Item>
      </ModalForm>
    </>
  );
};

export default SettingsModal;
