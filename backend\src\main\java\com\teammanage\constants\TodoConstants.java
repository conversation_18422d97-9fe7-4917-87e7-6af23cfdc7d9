package com.teammanage.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * Todo状态和优先级常量类
 * 
 * 定义Todo状态和优先级的标识符和映射关系
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TodoConstants {
    
    /**
     * Todo状态常量
     */
    public static class Status {
        /** 未完成状态码 */
        public static final int PENDING_CODE = 0;
        /** 已完成状态码 */
        public static final int COMPLETED_CODE = 1;
        
        /** 状态码到显示名称的映射 */
        private static final Map<Integer, String> CODE_TO_DISPLAY_NAME = new HashMap<>();
        
        static {
            CODE_TO_DISPLAY_NAME.put(PENDING_CODE, "未完成");
            CODE_TO_DISPLAY_NAME.put(COMPLETED_CODE, "已完成");
        }
        
        /**
         * 根据状态码获取显示名称
         * 
         * @param code 状态码
         * @return 显示名称，如果不存在则返回null
         */
        public static String getDisplayName(Integer code) {
            return CODE_TO_DISPLAY_NAME.get(code);
        }
        
        /**
         * 检查状态码是否有效
         * 
         * @param code 状态码
         * @return 是否有效
         */
        public static boolean isValidCode(Integer code) {
            return CODE_TO_DISPLAY_NAME.containsKey(code);
        }
        
        /**
         * 检查状态是否为已完成
         * 
         * @param code 状态码
         * @return 是否已完成
         */
        public static boolean isCompleted(Integer code) {
            return COMPLETED_CODE == code;
        }
        
        /**
         * 检查状态是否为未完成
         * 
         * @param code 状态码
         * @return 是否未完成
         */
        public static boolean isPending(Integer code) {
            return PENDING_CODE == code;
        }
    }
    
    /**
     * Todo优先级常量
     */
    public static class Priority {
        /** 低优先级状态码 */
        public static final int LOW_CODE = 1;
        /** 中优先级状态码 */
        public static final int MEDIUM_CODE = 2;
        /** 高优先级状态码 */
        public static final int HIGH_CODE = 3;
        
        /** 优先级码到显示名称的映射 */
        private static final Map<Integer, String> CODE_TO_DISPLAY_NAME = new HashMap<>();
        
        static {
            CODE_TO_DISPLAY_NAME.put(LOW_CODE, "低");
            CODE_TO_DISPLAY_NAME.put(MEDIUM_CODE, "中");
            CODE_TO_DISPLAY_NAME.put(HIGH_CODE, "高");
        }
        
        /**
         * 根据优先级码获取显示名称
         * 
         * @param code 优先级码
         * @return 显示名称，如果不存在则返回null
         */
        public static String getDisplayName(Integer code) {
            return CODE_TO_DISPLAY_NAME.get(code);
        }
        
        /**
         * 检查优先级码是否有效
         * 
         * @param code 优先级码
         * @return 是否有效
         */
        public static boolean isValidCode(Integer code) {
            return CODE_TO_DISPLAY_NAME.containsKey(code);
        }
        
        /**
         * 检查优先级是否为高优先级
         * 
         * @param code 优先级码
         * @return 是否为高优先级
         */
        public static boolean isHigh(Integer code) {
            return HIGH_CODE == code;
        }
        
        /**
         * 检查优先级是否为中优先级
         * 
         * @param code 优先级码
         * @return 是否为中优先级
         */
        public static boolean isMedium(Integer code) {
            return MEDIUM_CODE == code;
        }
        
        /**
         * 检查优先级是否为低优先级
         * 
         * @param code 优先级码
         * @return 是否为低优先级
         */
        public static boolean isLow(Integer code) {
            return LOW_CODE == code;
        }
    }
}
