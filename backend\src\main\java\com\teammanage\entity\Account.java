package com.teammanage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 用户账户实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("account")
public class Account extends BaseEntity {

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 255, message = "邮箱长度不能超过255个字符")
    private String email;

    /**
     * 密码哈希
     */
    @JsonIgnore
    @Size(max = 255, message = "密码哈希长度不能超过255个字符")
    private String passwordHash;

    /**
     * 用户名
     */
    @Size(max = 100, message = "用户名长度不能超过100个字符")
    private String name;

    /**
     * 手机号
     */
    @Size(max = 11, message = "手机号长度不能超过11个字符")
    private String telephone;

    /**
     * 当前套餐ID
     */
    private Long defaultSubscriptionPlanId;

}
