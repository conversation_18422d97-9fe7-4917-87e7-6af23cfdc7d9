package com.teammanage.util;

import com.teammanage.constants.InvitationStatusConstants;
import com.teammanage.constants.SubscriptionStatusConstants;
import com.teammanage.entity.AccountSubscription.SubscriptionStatus;
import com.teammanage.entity.TeamInvitation.InvitationStatus;
import com.teammanage.enums.TeamRole;

/**
 * 状态码转换工具类
 * 
 * 提供枚举与状态码之间的转换方法，支持渐进式迁移
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class StatusCodeConverter {
    
    /**
     * 团队角色相关转换
     */
    public static class TeamRoleConverter {
        
        /**
         * 枚举转状态码
         */
        public static Integer toCode(TeamRole role) {
            return role != null ? TeamRole.StatusCodes.toCode(role) : null;
        }
        
        /**
         * 状态码转枚举
         */
        public static TeamRole fromCode(Integer code) {
            return code != null ? TeamRole.StatusCodes.fromCode(code) : null;
        }
        
        /**
         * 检查状态码是否有效
         */
        public static boolean isValidCode(Integer code) {
            return TeamRole.StatusCodes.isValidCode(code);
        }
        
        /**
         * 获取默认邀请角色的状态码
         */
        public static Integer getDefaultInvitationRoleCode() {
            return toCode(TeamRole.getDefaultInvitationRole());
        }
    }
    
    /**
     * 邀请状态相关转换
     */
    public static class InvitationStatusConverter {
        
        /**
         * 枚举转状态码
         */
        public static Integer toCode(InvitationStatus status) {
            return status != null ? InvitationStatusConstants.toCode(status) : null;
        }
        
        /**
         * 状态码转枚举
         */
        public static InvitationStatus fromCode(Integer code) {
            return code != null ? InvitationStatusConstants.fromCode(code) : null;
        }
        
        /**
         * 检查状态码是否有效
         */
        public static boolean isValidCode(Integer code) {
            return InvitationStatusConstants.isValidCode(code);
        }
        
        /**
         * 获取待确认状态码
         */
        public static Integer getPendingCode() {
            return InvitationStatusConstants.PENDING_CODE;
        }
        
        /**
         * 获取已确认状态码
         */
        public static Integer getAcceptedCode() {
            return InvitationStatusConstants.ACCEPTED_CODE;
        }
        
        /**
         * 获取已拒绝状态码
         */
        public static Integer getRejectedCode() {
            return InvitationStatusConstants.REJECTED_CODE;
        }
        
        /**
         * 获取已取消状态码
         */
        public static Integer getCancelledCode() {
            return InvitationStatusConstants.CANCELLED_CODE;
        }
        
        /**
         * 检查状态是否可以被响应
         */
        public static boolean canBeResponded(Integer code) {
            return InvitationStatusConstants.canBeResponded(code);
        }
        
        /**
         * 检查状态是否可以被取消
         */
        public static boolean canBeCancelled(Integer code) {
            return InvitationStatusConstants.canBeCancelled(code);
        }
    }
    
    /**
     * 订阅状态相关转换
     */
    public static class SubscriptionStatusConverter {
        
        /**
         * 枚举转状态码
         */
        public static Integer toCode(SubscriptionStatus status) {
            return status != null ? SubscriptionStatusConstants.toCode(status) : null;
        }
        
        /**
         * 状态码转枚举
         */
        public static SubscriptionStatus fromCode(Integer code) {
            return code != null ? SubscriptionStatusConstants.fromCode(code) : null;
        }
        
        /**
         * 检查状态码是否有效
         */
        public static boolean isValidCode(Integer code) {
            return SubscriptionStatusConstants.isValidCode(code);
        }
        
        /**
         * 获取激活状态码
         */
        public static Integer getActiveCode() {
            return SubscriptionStatusConstants.ACTIVE_CODE;
        }
        
        /**
         * 获取取消状态码
         */
        public static Integer getCanceledCode() {
            return SubscriptionStatusConstants.CANCELED_CODE;
        }
        
        /**
         * 检查状态是否为激活状态
         */
        public static boolean isActive(Integer code) {
            return SubscriptionStatusConstants.isActive(code);
        }
    }
}
