package com.teammanage.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import com.teammanage.entity.Account;
import com.teammanage.entity.TeamMember;
import com.teammanage.enums.TeamRole;
import com.teammanage.exception.TeamAccessDeniedException;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.mapper.TeamMemberMapper;
import com.teammanage.util.JwtTokenUtil;

/**
 * AuthService 团队访问控制测试
 */
@ExtendWith(MockitoExtension.class)
class AuthServiceTeamAccessTest {

    @Mock
    private TeamMemberMapper teamMemberMapper;

    @Mock
    private AccountMapper accountMapper;

    @Mock
    private JwtTokenUtil jwtTokenUtil;

    @Mock
    private UserSessionService userSessionService;

    @InjectMocks
    private AuthService authService;

    private String validToken = "valid.jwt.token";
    private Long userId = 100L;
    private Long teamId = 1L;
    private Account testAccount;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testAccount = new Account();
        testAccount.setId(userId);
        testAccount.setEmail("<EMAIL>");
        testAccount.setName("Test User");

        // 模拟JWT工具类的行为
        when(jwtTokenUtil.validateToken(validToken)).thenReturn(true);
        when(jwtTokenUtil.getUserIdFromToken(validToken)).thenReturn(userId);
        lenient().when(accountMapper.selectById(userId)).thenReturn(testAccount);
    }

    @Test
    void testSelectTeam_Success_ActiveMember() {
        // 准备测试数据 - 活跃的团队成员
        TeamMember activeMember = createTeamMember(true, false, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(activeMember);

        // 模拟JWT生成和会话管理
        String newToken = "new.jwt.token";
        when(jwtTokenUtil.generateTokenWithTeam(testAccount, teamId, false)).thenReturn(newToken);
        when(jwtTokenUtil.getJtiFromToken(newToken)).thenReturn("new-jti");
        when(jwtTokenUtil.getJtiFromToken(validToken)).thenReturn("old-jti");
        when(jwtTokenUtil.getExpirationDateFromToken(newToken)).thenReturn(new java.util.Date(System.currentTimeMillis() + 3600000));

        // 执行测试
        assertDoesNotThrow(() -> authService.selectTeam(teamId, validToken));

        // 验证团队成员的最后访问时间被更新
        verify(teamMemberMapper).updateById(activeMember);
    }

    @Test
    void testSelectTeam_Fail_NotTeamMember() {
        // 准备测试数据 - 用户不是团队成员
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(null);

        // 执行测试 - 应该抛出异常
        TeamAccessDeniedException exception = assertThrows(
            TeamAccessDeniedException.class,
            () -> authService.selectTeam(teamId, validToken)
        );

        assertEquals("您不是该团队的成员", exception.getMessage());
        assertEquals(teamId, exception.getTeamId());
        assertEquals(userId, exception.getUserId());
        assertEquals("NOT_TEAM_MEMBER", exception.getReason());
    }

    @Test
    void testSelectTeam_Fail_InactiveMember() {
        // 准备测试数据 - 被停用的团队成员
        TeamMember inactiveMember = createTeamMember(false, false, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(inactiveMember);

        // 执行测试 - 应该抛出异常
        TeamAccessDeniedException exception = assertThrows(
            TeamAccessDeniedException.class,
            () -> authService.selectTeam(teamId, validToken)
        );

        assertEquals("您的账户已在此团队中被停用", exception.getMessage());
        assertEquals(teamId, exception.getTeamId());
        assertEquals(userId, exception.getUserId());
    }

    @Test
    void testSelectTeam_Fail_DeletedMember() {
        // 准备测试数据 - 被删除的团队成员
        TeamMember deletedMember = createTeamMember(true, true, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(deletedMember);

        // 执行测试 - 应该抛出异常
        TeamAccessDeniedException exception = assertThrows(
            TeamAccessDeniedException.class,
            () -> authService.selectTeam(teamId, validToken)
        );

        assertEquals("您已不是该团队的成员", exception.getMessage());
    }

    @Test
    void testSelectTeam_Success_TeamCreator() {
        // 准备测试数据 - 团队创建者（活跃状态）
        TeamMember creatorMember = createTeamMember(true, false, true);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(creatorMember);

        // 模拟JWT生成和会话管理
        String newToken = "new.jwt.token";
        when(jwtTokenUtil.generateTokenWithTeam(testAccount, teamId, true)).thenReturn(newToken);
        when(jwtTokenUtil.getJtiFromToken(newToken)).thenReturn("new-jti");
        when(jwtTokenUtil.getJtiFromToken(validToken)).thenReturn("old-jti");
        when(jwtTokenUtil.getExpirationDateFromToken(newToken)).thenReturn(new java.util.Date(System.currentTimeMillis() + 3600000));

        // 执行测试
        assertDoesNotThrow(() -> authService.selectTeam(teamId, validToken));

        // 验证使用了正确的创建者标识
        verify(jwtTokenUtil).generateTokenWithTeam(testAccount, teamId, true);
    }

    @Test
    void testSwitchTeam_UsesSelectTeamLogic() {
        // 准备测试数据 - 活跃的团队成员
        TeamMember activeMember = createTeamMember(true, false, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(activeMember);

        // 模拟JWT生成和会话管理
        String newToken = "new.jwt.token";
        when(jwtTokenUtil.generateTokenWithTeam(testAccount, teamId, false)).thenReturn(newToken);
        when(jwtTokenUtil.getJtiFromToken(newToken)).thenReturn("new-jti");
        when(jwtTokenUtil.getJtiFromToken(validToken)).thenReturn("old-jti");
        when(jwtTokenUtil.getExpirationDateFromToken(newToken)).thenReturn(new java.util.Date(System.currentTimeMillis() + 3600000));

        // 执行测试 - switchTeam应该调用selectTeam的逻辑
        assertDoesNotThrow(() -> authService.switchTeam(teamId, validToken));

        // 验证相同的验证逻辑被执行
        verify(teamMemberMapper).findByTeamIdAndAccountId(teamId, userId);
        verify(teamMemberMapper).updateById(activeMember);
    }

    /**
     * 创建测试用的团队成员对象
     */
    private TeamMember createTeamMember(boolean isActive, boolean isDeleted, boolean isCreator) {
        TeamMember member = new TeamMember();
        member.setTeamId(teamId);
        member.setAccountId(userId);
        member.setRole(isCreator ? TeamRole.TEAM_CREATOR : TeamRole.TEAM_MEMBER);
        member.setIsActive(isActive);
        member.setIsDeleted(isDeleted);
        return member;
    }
}
